#!/usr/bin/env bash

#编译
function compileDevelop() {
  git checkout feature/connect
  git pull origin feature/connect
  env GOOS=linux GOARCH=amd64 go build --race -o minesweep main.go
}

#编译
function compileTesting() {
  #git checkout feature/connect
  #git pull origin feature/connect
  env GOOS=linux GOARCH=amd64 go build --race -o minesweep main.go
}

#编译
function compileMaster() {
  git checkout feature/connect
  git pull origin feature/connect
  env GOOS=linux GOARCH=amd64 go build -o minesweep main.go
}

function help() {
  cat <<eof
  ./ctl.sh compile develop
  ./ctl.sh compile testing
  ./ctl.sh compile master
  ./ctl.sh log
  ./ctl.sh -h
eof
}

if [ "$1" == "compile" ] && [ "$2" == "develop" ]; then
  compileDevelop
elif [ "$1" == "compile" ] && [ "$2" == "testing" ]; then
  compileTesting
elif [ "$1" == "compile" ] && [ "$2" == "master" ]; then
  compileMaster
elif [ "$1" == "log" ]; then
  tail -f ./logs/game_route.log
elif [ "$1" == "-h" ]; then
  help
else
  help
fi
