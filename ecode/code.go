package ecode

import "ms-version.soofun.online/wjl/game_public/types_public"

// 错误码定义
const (
	OK               int = 0  // 操作成功
	ErrGameId        int = 1  // 错误的GameId
	ErrScreenMode    int = 2  // 无效的屏幕模式
	ErrGameMode      int = 3  // 无效的游戏模式
	ErrPropMode      int = 4  // 无效的道具玩法模式
	ErrRequestUser   int = 5  // 从平台请求用户信息失败
	ErrInPair        int = 6  // 玩家已经在匹配队列中
	ErrNotEnoughCoin int = 7  // 没有足够的金币
	ErrChangeBalance int = 8  // 扣除金币失败
	ErrNotFoundRoom  int = 9  // 没有找到指定的房间
	ErrNotFoundUser  int = 10 // 没有找到玩家信息
	ErrEnterRoom     int = 11 // 不能进入到该房间
	ErrRoomConfig    int = 12 // 房间配置出错
	ErrParams        int = 13 // 请求参数错误
	ErrSave          int = 14 // 保存db失败
	ErrForbidPair    int = 15 // 不支持匹配功能
	ErrDefend        int = 16 // 系统维护中
	ErrNotSit        int = 17 // 不是座位上玩家
	ErrSitHaveUser   int = 18 // 座位上已有其他玩家
	ErrHaveSit       int = 19 // 玩家已坐下
	ErrUserPlaying   int = 20 // 玩家游戏中
	ErrVisitorLimit  int = 21 // 游客被限制

	ErrNotFoundProduct    int = 24 // 要购买的商品不存在
	ErrAlreadyHaveProduct int = 25 // 已经拥有该商品了,无需重复购买
	ErrBuyProductFail     int = 26 // 购买商品失败
	ErrNotHaveSkin        int = 27 // 使用未拥有的皮肤
	ErrProductType        int = 28 // 无效的商品类型
	ErrGiftIdInvalid      int = 29 // 无效的礼物Id
	ErrGiftReceiver       int = 30 // 没有找到礼物接收者

	ErrInInvite          int = 31 // 已经在邀请中了
	ErrPlaying           int = 32 // 玩家已经在游戏中了
	ErrInvalidInviteCode int = 33 // 无效的邀请码
	ErrEnoughUser        int = 34 // 人够了,不能接受邀请
	ErrNotInvite         int = 35 // 不在邀请队列中
	ErrChgInvite         int = 36 // 修改邀请配置失败
	ErrNotInviteCreator  int = 37 // 不是邀请的创建者
	ErrForbidKickSelf    int = 38 // 不能踢除自己
	ErrInviteOff         int = 39 // 邀请功能未开启
	ErrInviteNotAllReady int = 40 // 还有玩家没有准备好
	ErrInviteStart       int = 41 // 开始游戏失败
	ErrNotInPair         int = 44 // 不在匹配队列中
	ErrGameExist         int = 45 // 游戏已存在
	ErrSignature         int = 46 // 验证签名失败
	ErrFusing            int = 47 // 奖池已熔断

	ErrAlreadyHaveProp = 61 // 玩家已拥有道具
	ErrBlockNoSuchProp = 62 // 块没有该道具
	ErrPropNotFound    = 63 // 道具不存在
	ErrPropUsing       = 64 // 道具使用中
	ErrBlockNoProp     = 65 // 块没有道具

	ErrNotFoundVoiceRoom     int = 101 // 指定的语聊房不存在
	ErrNoEnterVoiceRoom      int = 102 // 没有进入到语聊房,不能坐下
	ErrVoiceRoomSitPos       int = 103 // 坐下的位置不正确
	ErrVoicePosHaveUser      int = 104 // 目标游戏位置上已经有人了
	ErrVoiceUserFull         int = 105 // 游戏玩家已经坐满了
	ErrVoiceNoPower          int = 106 // 操作语聊房时,权限不足
	ErrVoicePlayerNotEnough  int = 108 // 开始游戏时, 座位上人数不足
	ErrVoiceNotAllReady      int = 109 // 有玩家未准备
	ErrVoiceNotEnoughCoin    int = 111 // 玩家金币不足
	ErrVoiceAlreadyStartGame int = 113 // 语聊房的游戏已经开始
)

func GetMsg(code int) string {
	errMsg := ""
	switch code {
	case OK:
		errMsg = "成功"
	case ErrGameId:
		errMsg = "错误的GameId"
	case ErrScreenMode:
		errMsg = "无效的屏幕模式"
	case ErrGameMode:
		errMsg = "无效的游戏模式"
	case ErrPropMode:
		errMsg = "无效的道具玩法模式"
	case ErrRequestUser:
		errMsg = "从平台请求用户信息失败"
	case ErrInPair:
		errMsg = "玩家已经在匹配队列中"
	case ErrNotEnoughCoin:
		errMsg = "没有足够的金币"
	case ErrChangeBalance:
		errMsg = "扣除金币失败"
	case ErrNotFoundRoom:
		errMsg = "没有找到房间"
	case ErrNotFoundUser:
		errMsg = "没有找到玩家信息"
	case ErrEnterRoom:
		errMsg = "不能进入到该房间"
	case ErrRoomConfig:
		errMsg = "房间配置出错"
	case ErrParams:
		errMsg = "请求参数错误"
	case ErrSave:
		errMsg = "保存失败"
	case ErrForbidPair:
		errMsg = "不支持匹配功能"
	case ErrDefend:
		errMsg = "维护中"
	case ErrNotSit:
		errMsg = "不是座位上玩家"
	case ErrUserPlaying:
		errMsg = "玩家正在游戏中"

	case ErrNotFoundProduct:
		errMsg = "要购买的商品不存在"
	case ErrAlreadyHaveProduct:
		errMsg = "已经拥有该商品了,无需重复购买"
	case ErrBuyProductFail:
		errMsg = "购买失败"
	case ErrNotHaveSkin:
		errMsg = "使用未拥有的皮肤"
	case ErrProductType:
		errMsg = "无效的商品类型"
	case ErrGiftIdInvalid:
		errMsg = "无效的礼物Id"
	case ErrGiftReceiver:
		errMsg = "没有找到礼物接受者"

	case ErrInInvite:
		errMsg = "已经在邀请中了"
	case ErrPlaying:
		errMsg = "玩家已经在游戏中了"
	case ErrInvalidInviteCode:
		errMsg = "无效的邀请码"
	case ErrEnoughUser:
		errMsg = "人够了,不能接受邀请"
	case ErrNotInvite:
		errMsg = "不在邀请队列中"
	case ErrChgInvite:
		errMsg = "修改邀请配置失败"
	case ErrNotInviteCreator:
		errMsg = "不是邀请的创建者"
	case ErrForbidKickSelf:
		errMsg = "不能踢除自己"
	case ErrInviteOff:
		errMsg = "邀请功能未开启"
	case ErrInviteNotAllReady:
		errMsg = "还有玩家没有准备好"
	case ErrInviteStart:
		errMsg = "开始游戏失败"
	case ErrNotInPair:
		errMsg = "不在匹配队列中"
	case ErrGameExist:
		errMsg = "游戏已存在"
	case ErrSignature:
		errMsg = "签名校验失败"
	case ErrFusing:
		errMsg = "奖池已熔断"
	case ErrAlreadyHaveProp:
		errMsg = "已拥有道具"
	case ErrBlockNoSuchProp:
		errMsg = "块没有该道具"
	case ErrPropNotFound:
		errMsg = "道具不存在"
	case ErrPropUsing:
		errMsg = "道具使用中"
	case ErrNotFoundVoiceRoom:
		errMsg = "指定的语聊房不存在"
	case ErrNoEnterVoiceRoom:
		errMsg = "没有进入到语聊房,不能坐下"
	case ErrVoiceRoomSitPos:
		errMsg = "坐下的位置不正确"
	case ErrVoicePosHaveUser:
		errMsg = "目标游戏位置上已经有人了"
	case ErrVoiceUserFull:
		errMsg = "游戏玩家已经坐满了"
	case ErrVoiceNoPower:
		errMsg = "操作语聊房时,权限不足"
	case ErrVoicePlayerNotEnough:
		errMsg = "开始游戏时,人数不足"
	case ErrVoiceNotAllReady:
		errMsg = "还有玩家没有准备好"
	case ErrVoiceNotEnoughCoin:
		errMsg = "开始游戏时,有玩家金币不足"
	case ErrVoiceAlreadyStartGame:
		errMsg = "语聊房的游戏已经开始"
	default:
		errMsg = types_public.ZegoGameResCode(code).String()
	}
	return errMsg
}
