package model

import (
	"bytes"
	"fmt"
	"gorm.io/gorm"
	"minesweep/common/gormx"
	"minesweep/common/logx"
	"minesweep/constvar"
	"time"
)

type (
	UserSkin struct {
		ID         uint               `gorm:"column:Id;primary_key"`
		AppChannel string             `gorm:"column:AppChannel;type:varchar(50);default:'';index:idx_aau,unique"` // 渠道ID
		AppID      int64              `gorm:"column:AppID;type:bigint;default:0;index:idx_aau,unique"`            // appID
		UserID     string             `gorm:"column:UserID;type:varchar(50);default:'';index:idx_aau,unique"`     // 用户ID
		ChessID    constvar.ProductID `gorm:"column:ChessID;type:int;default:0"`                                  // 棋子的皮肤ID
		CreateTime time.Time          `gorm:"column:CreateTime;type:datetime"`                                    // 创建时间
	}

	UserSkinSearch struct {
		Orm *gorm.DB
		UserSkin
		Page
		Order string
	}
)

// TableName 指定表名.
func (*UserSkin) TableName() string {
	return "user_skin"
}

func NewUserSkinSearch() *UserSkinSearch {
	return &UserSkinSearch{Orm: gormx.GetDB()}
}

func (slf *UserSkinSearch) SetOrm(tx *gorm.DB) *UserSkinSearch {
	slf.Orm = tx
	return slf
}

func (slf *UserSkinSearch) SetPage(page, size int) *UserSkinSearch {
	slf.PageNum, slf.PageSize = page, size
	return slf
}

func (slf *UserSkinSearch) SetOrder(order string) *UserSkinSearch {
	slf.Order = order
	return slf
}

func (slf *UserSkinSearch) SetUserID(userID string) *UserSkinSearch {
	slf.UserID = userID
	return slf
}

func (slf *UserSkinSearch) SetAppChannel(appChannel string) *UserSkinSearch {
	slf.AppChannel = appChannel
	return slf
}

func (slf *UserSkinSearch) SetAppID(AppID int64) *UserSkinSearch {
	slf.AppID = AppID
	return slf
}

// Create 单条插入
func (slf *UserSkinSearch) Create(recordM *UserSkin) (uint, error) {
	if err := slf.Orm.Create(recordM).Error; err != nil {
		return 0, fmt.Errorf("create recordM err: %v, recordM: %+v", err, recordM)
	}

	return recordM.ID, nil
}

// CreateBatch 批量插入
func (slf *UserSkinSearch) CreateBatch(records []*UserSkin) error {
	if err := slf.Orm.Create(&records).Error; err != nil {
		return fmt.Errorf("create records err: %v, records: %+v", err, records)
	}

	return nil
}

// build 构建条件.
func (slf *UserSkinSearch) build() *gorm.DB {
	var db = slf.Orm.Table(slf.TableName()).Model(UserSkin{})

	if slf.UserID != "" {
		db = db.Where("UserID = ?", slf.UserID)
	}

	if slf.AppChannel != "" {
		db = db.Where("AppChannel = ?", slf.AppChannel)
	}

	if slf.AppID > 0 {
		db = db.Where("AppID = ?", slf.AppID)
	}

	if slf.Order != "" {
		db = db.Order(slf.Order)
	}

	return db
}

// Find 多条查询.
func (slf *UserSkinSearch) Find() ([]*UserSkin, int64, error) {
	var (
		records = make([]*UserSkin, 0)
		total   int64
		db      = slf.build()
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find count err: %v", err)
	}
	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find records err: %v", err)
	}

	return records, total, nil
}

// FindByQuery 指定条件查询.
func (slf *UserSkinSearch) FindByQuery(query string, args []interface{}) ([]*UserSkin, int64, error) {
	var (
		records = make([]*UserSkin, 0)
		total   int64
		db      = slf.Orm.Table(slf.TableName()).Where(query, args...)
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find by query count err: %v", err)
	}
	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find by query records err: %v, query: %s, args: %+v", err, query, args)
	}

	return records, total, nil
}

// FindByQueryNotTotal 指定条件查询&不查询总条数.
func (slf *UserSkinSearch) FindByQueryNotTotal(query string, args []interface{}) ([]*UserSkin, error) {
	var (
		records = make([]*UserSkin, 0)
		db      = slf.Orm.Table(slf.TableName()).Where(query, args...)
	)

	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, fmt.Errorf("find by query records err: %v, query: %s, args: %+v", err, query, args)
	}

	return records, nil
}

// First 单条查询.
func (slf *UserSkinSearch) First() (*UserSkin, error) {
	var (
		recordM = new(UserSkin)
		db      = slf.build()
	)

	if err := db.First(recordM).Error; err != nil {
		return recordM, err
	}

	return recordM, nil
}

// UpdateByMap 更新.
func (slf *UserSkinSearch) UpdateByMap(upMap map[string]interface{}) error {
	var (
		db = slf.build()
	)

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by map err: %v, upMap: %+v", err, upMap)
	}

	return nil
}

// UpdateByQuery 指定条件更新.
func (slf *UserSkinSearch) UpdateByQuery(query string, args []interface{}, upMap map[string]interface{}) error {
	var (
		db = slf.Orm.Table(slf.TableName()).Where(query, args...)
	)

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by query err: %v, query: %s, args: %+v, upMap: %+v", err, query, args, upMap)
	}

	return nil
}

// BulkUpsert 批量插入
func (slf *UserSkinSearch) BulkUpsert(userSkins []*UserSkin) error {
	if len(userSkins) <= 0 {
		return nil
	}
	var buffer bytes.Buffer
	sql := fmt.Sprintf("insert into `%v` (`AppChannel`,`AppID`,`UserID`,`ChessID`,`CreateTime`) values", slf.TableName())
	if _, err := buffer.WriteString(sql); err != nil {
		return err
	}

	var list []interface{}
	for i, e := range userSkins {
		buffer.WriteString(fmt.Sprintf("(?,?,?,?,?)"))
		list = append(list, e.AppChannel, e.AppID, e.UserID, e.ChessID, e.CreateTime)
		if i == len(userSkins)-1 {
			buffer.WriteString("")
		} else {
			buffer.WriteString(",")
		}
	}
	buffer.WriteString(fmt.Sprintf("on duplicate key update `ChessID`=values(`ChessID`);"))

	err := slf.Orm.Exec(buffer.String(), list...).Error
	if err != nil {
		logx.Errorf("BulkUpsert sql:%v, err:%v", buffer.String(), err)
	}
	return err
}
