package base

type CreateWs struct {
	AppChannel string `json:"appChannel"` // 平台渠道
	AppID      int64  `json:"appId"`      // appID
	UserID     string `json:"userId"`     // 玩家ID
	FromID     string `json:"fromId"`     // 来源服务器ID
}

type Shutdown struct {
	SrvID string `json:"srvId"` // 游戏服务器ID
}

type VoiceShutdown struct {
	AppChannel string `json:"appChannel"` // 平台渠道
	AppID      int64  `json:"appId"`      // appID
	PlatRoomID string `json:"platRoomId"` // 语聊房Id
}
