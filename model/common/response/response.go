package response

import (
	"minesweep/ecode"
	"net/http"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

func Result(code int, data interface{}, msg string, c *gin.Context) {
	// 开始时间
	c.J<PERSON>(http.StatusOK, Response{
		code,
		data,
		msg,
	})
}

func Ok(c *gin.Context) {
	Result(ecode.OK, map[string]interface{}{}, "成功", c)
}

func OkWithDetailed(data interface{}, c *gin.Context) {
	Result(ecode.OK, data, "success", c)
}

func Fail(code int, c *gin.Context) {
	Result(code, map[string]interface{}{}, ecode.GetMsg(code), c)
}

func FailWithDetailed(data interface{}, code int, c *gin.Context) {
	Result(code, data, ecode.GetMsg(code), c)
}

func FailV2(code int, message string, c *gin.Context) {
	Result(code, map[string]interface{}{}, message, c)
}

func FailWithDetailedV2(data interface{}, code int, message string, c *gin.Context) {
	Result(code, data, message, c)
}
