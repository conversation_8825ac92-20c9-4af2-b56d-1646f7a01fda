package response

import (
	"minesweep/constvar"
	"time"
)

type (
	// VoiceRoomUser 语聊房 玩家信息
	VoiceRoomUser struct {
		UserID   string        `json:"userId"`   // 玩家id
		NickName string        `json:"nickName"` // 昵称
		Avatar   string        `json:"avatar"`   // 头像
		Pos      int           `json:"pos"`      // 座位号
		Ready    bool          `json:"ready"`    // 是否准备
		Robot    bool          `json:"robot"`    // 是否机器人
		Role     constvar.Role `json:"role"`     // 角色
	}

	// VoiceRoomInfo 语聊房 房间信息
	VoiceRoomInfo struct {
		PlatRoomID string          `json:"platRoomID"` // 语聊房Id
		PlayerNum  int             `json:"playerNum"`  // 玩家人数
		Fee        int             `json:"fee"`        // 房间费
		MapType    int             `json:"mapType"`    // 方格数
		PropMode   int             `json:"propMode"`   // 0 无道具模式 1 有道具模式
		Users      []VoiceRoomUser `json:"users"`      // 玩家列表
		RoomID     int64           `json:"roomId"`     // 与之相关的游戏房间Id
		KickOut    bool            `json:"kickOut"`    // true 允许踢除玩家
	}
)

// NoticeVoiceRoomCfg 改变语聊房配置
type NoticeVoiceRoomCfg struct {
	RoomInfo   VoiceRoomInfo `json:"roomInfo"`
	NewViewers []string      `json:"newViewers"` // 由2/4人模式变化 引起的把 已经坐下的人 变为观众
}

// NoticeVoiceUserStatus 广播 语聊房的玩家准备状态
type NoticeVoiceUserStatus struct {
	UserID string `json:"userId"`
	Ready  bool   `json:"ready"`
}

// NoticeVoiceKickOut 踢人
type NoticeVoiceKickOut struct {
	UserID   string `json:"userId"`
	Operator string `json:"operator"`
}

// NoticeVoiceChangeRole 广播 语聊房的玩家改变角色
type NoticeVoiceChangeRole struct {
	UserID string        `json:"userId"`
	Role   constvar.Role `json:"role"` // 角色
}

// QueryVoiceStatus 查询语聊房信息
type QueryVoiceStatus struct {
	PlatRoomId   string   `json:"plat_room_id"`   // 商户平台语聊房Id
	GameId       int      `json:"game_id"`        // 游戏Id
	MaxUserCount int      `json:"max_user_count"` // 需要的最大人数
	CurUserCount int      `json:"cur_user_count"` // 当前上座的人数
	PropMode     int      `json:"prop_mode"`      // 是否是道具模式
	StartTime    int64    `json:"start_time"`     // 开始时间戳,毫秒
	Attendance   float64  `json:"attendance"`     // 上座率
	Users        []string `json:"users"`          // 当前上座的玩家ID列表
}

func (s *QueryVoiceStatus) GetSortScore() int64 {
	var score int64 = 0
	score = int64(s.MaxUserCount * 100000000)
	score += int64(s.CurUserCount * 10000000)
	score += int64(s.PropMode * 1000000)
	score += (time.Now().UnixMilli() - s.StartTime) % 1000000
	return score
}

type CreateGame struct {
	GameID  int   `json:"game_id"`  // 游戏id
	RoomID  int64 `json:"room_id"`  // 游戏房间id
	StartAt int64 `json:"start_at"` // 游戏房间创建的时间戳,毫秒级
}
