package request

import "connect/constvar"

// VoiceEnterRoom 进入语聊房
type VoiceEnterRoom struct {
	RoomID string `json:"roomId"` // app的语聊房Id
}

// VoiceChangeRole 改变角色
type VoiceChangeRole struct {
	Role constvar.Role `json:"role"` // 要变更的新角色类型
}

// QueryVoiceRoom 渠道app查询语聊房列表
type QueryVoiceRoom struct {
	AppChannel string `json:"app_channel"` // 渠道名称
	AppId      int64  `json:"app_id"`      // AppId
}

// CreateGame 渠道app服务端匹配好玩家直接创建游戏
type (
	CreateGame struct {
		AppChannel     string      `json:"app_channel"`
		AppId          int64       `json:"app_id"`
		PlatRoomId     string      `json:"plat_room_id"`    // 平台的房间Id
		GameId         int         `json:"game_id"`         // 游戏ID
		ApiScene       int         `json:"api_scene"`       // api 调用场景 1-快速开始模式 2-PK模式(非必须)
		Bet            int         `json:"bet"`             // 入场费
		Users          []*UserInfo `json:"users"`           // 玩家列表
		SignatureNonce string      `json:"signature_nonce"` // 签名-随机数
		Timestamp      int64       `json:"timestamp"`       // 签名-时间戳
		Signature      string      `json:"signature"`       // 签名
	}

	UserInfo struct {
		UserId   string `json:"user_id"`   // 玩家Id
		NickName string `json:"nick_name"` // 玩家昵称
		Avatar   string `json:"avatar"`    // 玩家头像地址
		Role     string `json:"role"`      // 玩家角色
		Token    string `json:"token"`     // token(非必须)
	}

	CloseGame struct {
		AppChannel     string `json:"app_channel"`
		AppId          int64  `json:"app_id"`
		PlatRoomId     string `json:"plat_room_id"`    // 平台的房间Id
		GameId         int    `json:"game_id"`         // 游戏ID
		SignatureNonce string `json:"signature_nonce"` // 签名-随机数
		Timestamp      int64  `json:"timestamp"`       // 签名-时间戳
		Signature      string `json:"signature"`       // 签名
	}

	UserLeave struct {
		AppChannel string `json:"app_channel"`
		AppId      int64  `json:"app_id"`
		PlatRoomId string `json:"plat_room_id"` // 平台的房间Id
		GameId     int    `json:"game_id"`      // 游戏ID
		UserId     string `json:"user_id"`      // 玩家Id
	}

	AddRobot struct {
		AppChannel     string      `json:"app_channel"`
		AppId          int64       `json:"app_id"`
		PlatRoomId     string      `json:"plat_room_id"`    // 平台的房间Id
		GameId         int         `json:"game_id"`         // 游戏ID
		Users          []*UserInfo `json:"users"`           // 玩家列表
		Extend         string      `json:"extend"`          // 扩展参数
		SignatureNonce string      `json:"signature_nonce"` // 签名-随机数
		Timestamp      int64       `json:"timestamp"`       // 签名-时间戳
		Signature      string      `json:"signature"`       // 签名
	}

	KickOutRobot struct {
		AppChannel     string `json:"app_channel"`
		AppId          int64  `json:"app_id"`
		PlatRoomId     string `json:"plat_room_id"`    // 平台的房间Id
		GameId         int    `json:"game_id"`         // 游戏ID
		UserId         string `json:"user_id"`         // 玩家Id
		SignatureNonce string `json:"signature_nonce"` // 签名-随机数
		Timestamp      int64  `json:"timestamp"`       // 签名-时间戳
		Signature      string `json:"signature"`       // 签名
	}
)
