package request

// PageInfo Paging common input parameter structure
type PageInfo struct {
	Page     int `json:"page"`     // 页码
	PageSize int `json:"pageSize"` // 每页大小
}

// ExtendInfo 扩展信息
type ExtendInfo struct {
	AppChannel string `json:"appChannel"` // 平台渠道
	AppID      int64  `json:"appId"`      // appID
	UserID     string `json:"userId"`     // 玩家ID
}

// PackMessage push到房间的消息
type PackMessage struct {
	MsgID string      `json:"msgId"` // 消息ID
	Data  interface{} `json:"data"`  // 消息体
	Ext   ExtendInfo  `json:"ext"`   // 扩展信息
}
