package dao

import (
	"connect/common/redisx"
	"connect/conf"
	"context"
	"encoding/json"
	"fmt"
)

type GameSrvStat struct {
	SrvID       string // 游戏服务器Id
	RoomCount   int    // 房间数量
	PlayerCount int    // 玩家数量
	ViewerCount int    // 观众数量
}

func (c *GameSrvStat) Key() string {
	key := fmt.Sprintf("%v:gameSrvStat", conf.Conf.Server.Project)
	return key
}

func (c *GameSrvStat) HGetAll() ([]*GameSrvStat, error) {
	values, err := redisx.GetClient().HGetAll(context.TODO(), c.Key()).Result()
	if err != nil {
		return []*GameSrvStat{}, err
	}

	var list = make([]*GameSrvStat, 0)
	for _, v := range values {
		item := &GameSrvStat{}
		err = json.Unmarshal([]byte(v), item)
		if err != nil {
			continue
		}
		list = append(list, item)
	}
	return list, nil
}
