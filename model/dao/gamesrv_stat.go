package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"minesweep/common/redisx"
	"minesweep/conf"
)

// GameSrvStat 定时更新游戏服务器统计
type GameSrvStat struct {
	SrvID       string // 游戏服务器Id
	RoomCount   int    // 房间数量
	PlayerCount int    // 玩家数量
	RobotCount  int    // 机器人数量
	ViewerCount int    // 观众数量
}

func (c *GameSrvStat) Key() string {
	key := fmt.Sprintf("%v:gameSrvStat", conf.Conf.Server.Project)
	return key
}

func (c *GameSrvStat) HSet(stat *GameSrvStat) error {
	dataBytes, err := json.Marshal(stat)
	if err != nil {
		return err
	}

	_, err = redisx.GetClient().HSet(context.TODO(), c.Key(), conf.Conf.Server.ID, string(dataBytes)).Result()
	return err
}
