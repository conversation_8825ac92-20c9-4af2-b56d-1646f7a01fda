package dao

import (
	"connect/common/redisx"
	"connect/conf"
	"context"
	"encoding/json"
	"fmt"
	"time"
)

type VoiceInfo struct {
	SrvID     string // 游戏服务器ID
	FreshTime int64  // 刷新时间戳，秒(判断这个语聊房是否还存活)
}

func (c *VoiceInfo) Key(appChannel string, appID int64, platRoomID string) string {
	key := fmt.Sprintf("%v:voiceInfo:%v_%v_%v", conf.Conf.Server.Project, appChannel, appID, platRoomID)
	return key
}

func (c *VoiceInfo) Set(appChannel string, appID int64, platRoomID string, info *VoiceInfo) error {
	dataBytes, err := json.Marshal(info)
	if err != nil {
		return err
	}

	_, err = redisx.GetClient().Set(context.TODO(), c.Key(appChannel, appID, platRoomID), string(dataBytes), time.Hour*24*30).Result()
	return err
}

func (c *VoiceInfo) Get(appChannel string, appID int64, platRoomID string) (*VoiceInfo, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(appChannel, appID, platRoomID)).Result()
	if err != nil {
		return &VoiceInfo{}, err
	}

	var data = new(VoiceInfo)
	err = json.Unmarshal([]byte(value), data)
	return data, err
}

func (c *VoiceInfo) Delete(appChannel string, appID int64, platRoomID string) {
	_, _ = redisx.GetClient().Del(context.TODO(), c.Key(appChannel, appID, platRoomID)).Result()
}
