package dao

import (
	"connect/common/redisx"
	"connect/conf"
	"context"
	"encoding/json"
	"fmt"
)

type (
	ChannelConf struct {
		HideCoin    bool          `json:"hideCoin"`    // 是否隐藏金币
		CoinType    int           `json:"coinType"`    // 支持的货币类型,默认为0, 兼容老的GameCoin
		RoomConfigs []interface{} `json:"roomConfigs"` // 房间配置
	}
)

func (c *ChannelConf) Key(appChannel string, appID int64) string {
	key := fmt.Sprintf("%v:channelCfg:%v:%v", conf.Conf.Server.Project, appChannel, appID)
	return key
}

func (c *ChannelConf) Get(appChannel string, appID int64) (*ChannelConf, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(appChannel, appID)).Result()
	if err != nil {
		return &ChannelConf{}, err
	}

	var data = new(ChannelConf)
	err = json.Unmarshal([]byte(value), data)
	return data, err
}
