# 方块地图坐标系统说明

## 📍 坐标系统概述

根据代码分析，方块地图的二维数组坐标系统如下：

### 🎯 起始点位置
**起始点：左上角 (0, 0)**

### 📐 坐标轴方向
- **X轴**：从左到右递增 (0 → 7)
- **Y轴**：从上到下递增 (0 → 7)

## 🗺️ 8×8地图坐标布局

```
    X轴 →
Y   0   1   2   3   4   5   6   7
轴  ┌───┬───┬───┬───┬───┬───┬───┬───┐
↓ 0 │0,0│1,0│2,0│3,0│4,0│5,0│6,0│7,0│
    ├───┼───┼───┼───┼───┼───┼───┼───┤
  1 │0,1│1,1│2,1│3,1│4,1│5,1│6,1│7,1│
    ├───┼───┼───┼───┼───┼───┼───┼───┤
  2 │0,2│1,2│2,2│3,2│4,2│5,2│6,2│7,2│
    ├───┼───┼───┼───┼───┼───┼───┼───┤
  3 │0,3│1,3│2,3│3,3│4,3│5,3│6,3│7,3│
    ├───┼───┼───┼───┼───┼───┼───┼───┤
  4 │0,4│1,4│2,4│3,4│4,4│5,4│6,4│7,4│
    ├───┼───┼───┼───┼───┼───┼───┼───┤
  5 │0,5│1,5│2,5│3,5│4,5│5,5│6,5│7,5│
    ├───┼───┼───┼───┼───┼───┼───┼───┤
  6 │0,6│1,6│2,6│3,6│4,6│5,6│6,6│7,6│
    ├───┼───┼───┼───┼───┼───┼───┼───┤
  7 │0,7│1,7│2,7│3,7│4,7│5,7│6,7│7,7│
    └───┴───┴───┴───┴───┴───┴───┴───┘
```

## 💻 代码实现细节

### 1. 数据结构定义
```go
type MineMap struct {
    Width  int           // 地图宽度(8)
    Height int           // 地图高度(8)
    Blocks [][]MineBlock  // 地图块二维数组[y][x]
}

type MineBlock struct {
    X int `json:"x"` // x坐标(0-7)
    Y int `json:"y"` // y坐标(0-7)
}
```

### 2. 数组访问方式
```go
// 访问坐标(x, y)的方块
block := mineMap.Blocks[y][x]

// 例如：访问右下角的方块
block := mineMap.Blocks[7][7] // 坐标(7, 7)
```

### 3. 坐标转换逻辑
```go
// 一维索引转二维坐标
pos := 0  // 一维索引
x := pos % m.Width   // x = 0 % 8 = 0
y := pos / m.Width   // y = 0 / 8 = 0
// 结果：(0, 0) - 左上角

pos := 63 // 一维索引
x := pos % m.Width   // x = 63 % 8 = 7
y := pos / m.Width   // y = 63 / 8 = 7
// 结果：(7, 7) - 右下角
```

## 🎮 游戏中的应用

### 1. 玩家操作
```go
// 玩家点击请求
{
    "x": 3,  // 第4列（从0开始）
    "y": 2,  // 第3行（从0开始）
    "action": 1
}

// 对应访问
block := mineMap.Blocks[2][3]
```

### 2. 邻居计算
```go
// 8个方向的偏移量（3×3邻域）
directions := [][]int{
    {-1, -1}, {-1, 0}, {-1, 1}, // 上方三个
    {0, -1},           {0, 1},   // 左右两个
    {1, -1},  {1, 0},  {1, 1},   // 下方三个
}

// 计算邻居坐标
for _, dir := range directions {
    nx, ny := x+dir[0], y+dir[1]  // 注意：dir[0]是x偏移，dir[1]是y偏移
    if nx >= 0 && nx < m.Width && ny >= 0 && ny < m.Height {
        neighbor := m.Blocks[ny][nx]
    }
}
```

## 🔍 关键要点

1. **数组索引顺序**：`Blocks[y][x]` - 先Y后X
2. **坐标范围**：X和Y都是 0-7（8×8地图）
3. **起始点**：左上角 (0, 0)
4. **结束点**：右下角 (7, 7)
5. **访问模式**：`mineMap.Blocks[行][列]`

## 📱 前端对应关系

如果前端使用标准的屏幕坐标系统：
- **后端 (0, 0)** → **前端左上角**
- **后端 (7, 0)** → **前端右上角**
- **后端 (0, 7)** → **前端左下角**
- **后端 (7, 7)** → **前端右下角**

这种坐标系统符合大多数UI框架的标准，便于前后端数据同步。
