package persistmgr

import (
	"minesweep/cache"
	"minesweep/common/logx"
	"minesweep/common/safe"
	"minesweep/conf"
	"minesweep/model"
	"minesweep/model/dao"
	"sync"
	"time"
)

type GameRecordPersist struct {
	stopCh chan int
	stopWg sync.WaitGroup
}

func NewGameRecordPersist() *GameRecordPersist {
	return &GameRecordPersist{
		stopCh: make(chan int),
	}
}

func (slf *GameRecordPersist) Start() {
	slf.stopWg.Add(1)
	safe.Go(func() {
		defer slf.stopWg.Done()
		for {
			select {
			case <-slf.stopCh:
				return
			case <-time.After(time.Minute):
				// 防止list异常增长
				lLen := dao.GroupDao.GameRecordPersist.LLen()
				if lLen <= 500 {
					continue
				}

				logx.Errorf("GameRecordPersist listLen:%v err", lLen)
				slf.BulkUpsert()
			default:
			}

			if conf.Conf.Server.ID != "s1" {
				time.Sleep(time.Second * 2)
				continue
			}

			slf.BulkUpsert()
		}
	})
}

func (slf *GameRecordPersist) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}

func (slf *GameRecordPersist) BulkUpsert() {
	strList, _ := dao.GroupDao.GameRecordPersist.List()
	if len(strList) <= 0 {
		time.Sleep(time.Second * 2)
		return
	}

	var list []*model.GameRecord
	for _, v := range strList {
		item := &model.GameRecord{}
		err := jsonIterator.Unmarshal([]byte(v), item)
		if err != nil {
			logx.Errorf("Unmarshal err:%v, item:%+v", err, v)
			continue
		}
		list = append(list, item)
	}

	if err := slf.CreateBatch(list); err != nil {
		logx.Errorf("CreateBatch err:%v", err)
		_ = dao.GroupDao.GameRecordPersist.AddByRetry(list)
		time.Sleep(time.Second * 3)
	}
}

func (slf *GameRecordPersist) CreateBatch(records []*model.GameRecord) error {
	if len(records) <= 0 {
		return nil
	}

	upsertRecords := make(map[string][]*model.GameRecord)
	for _, v := range records {
		tableName := model.GameRecordTableName(v.ChangeTime)
		upsertRecords[tableName] = append(upsertRecords[tableName], v)
	}

	// 检查表是否存在，不存在则创建
	for tableName, _ := range upsertRecords {
		err := cache.GetInstance().CreateGameRecordTable(tableName, true)
		if err != nil {
			logx.Errorf("GameRecordPersist CreateGameRecordTable tableName:%v, err:%v", tableName, err)
			return err
		}
	}

	for tableName, list := range upsertRecords {
		err := model.NewGameRecordSearch().SetTableName(tableName).CreateBatch(list)
		if err != nil {
			logx.Errorf("GameRecordPersist CreateBatch tableName:%v, err:%v", tableName, err)
			_ = cache.GetInstance().CreateGameRecordTable(tableName, false)
			return model.NewGameRecordSearch().SetTableName(tableName).CreateBatch(list)
		}
	}
	return nil
}
