package main

import (
	"fmt"
	"minesweep/roommgr"
)

func main() {
	fmt.Println("=== 测试新坐标系统（左下角为起始点，Y轴从下往上）===")
	
	// 创建一个测试地图
	mineMap := roommgr.NewMineMap(12345)
	
	fmt.Printf("地图创建成功，大小: %dx%d，地雷数量: %d\n", 
		mineMap.Width, mineMap.Height, mineMap.MineCount)
	
	// 测试四个角的坐标
	fmt.Println("\n=== 测试四个角的坐标 ===")
	corners := []struct{
		name string
		x, y int
	}{
		{"左下角", 0, 0},
		{"右下角", 7, 0},
		{"左上角", 0, 7},
		{"右上角", 7, 7},
	}
	
	for _, corner := range corners {
		block := mineMap.GetBlock(corner.x, corner.y)
		if block != nil {
			fmt.Printf("%s (%d,%d): 存储的坐标=(%d,%d), 地雷=%v, 周围地雷数=%d\n", 
				corner.name, corner.x, corner.y, block.X, block.Y, block.IsMine, block.NeighborMines)
		}
	}
	
	// 打印地雷分布（按新坐标系统显示）
	fmt.Println("\n=== 地雷分布（新坐标系统：Y轴从下往上）===")
	fmt.Println("Y轴")
	fmt.Println("↑")
	
	// 从上往下打印（Y=7到Y=0）
	for logicY := 7; logicY >= 0; logicY-- {
		fmt.Printf("%d ", logicY)
		for x := 0; x < 8; x++ {
			block := mineMap.GetBlock(x, logicY)
			if block == nil {
				fmt.Print("? ")
				continue
			}
			if block.IsMine {
				fmt.Print("* ")
			} else {
				fmt.Printf("%d ", block.NeighborMines)
			}
		}
		fmt.Println()
	}
	fmt.Println("  0 1 2 3 4 5 6 7 → X轴")
	
	// 验证GetMinePositions返回的坐标
	fmt.Println("\n=== 地雷位置列表 ===")
	minePositions := mineMap.GetMinePositions()
	fmt.Printf("地雷总数: %d\n", len(minePositions))
	for i, pos := range minePositions {
		fmt.Printf("地雷%d: (%d,%d)\n", i+1, pos[0], pos[1])
	}
	
	// 测试连锁展开（寻找空白格）
	fmt.Println("\n=== 测试连锁展开 ===")
	var testX, testY int = -1, -1
	for y := 0; y < 8; y++ {
		for x := 0; x < 8; x++ {
			block := mineMap.GetBlock(x, y)
			if block != nil && !block.IsMine && block.NeighborMines == 0 {
				testX, testY = x, y
				break
			}
		}
		if testX != -1 {
			break
		}
	}
	
	if testX != -1 {
		fmt.Printf("找到空白格位置: (%d, %d)\n", testX, testY)
		
		// 先手动揭示起始点
		block := mineMap.GetBlock(testX, testY)
		if block != nil {
			block.IsRevealed = true
			mineMap.RevealedCount++
			
			// 测试连锁展开
			result := mineMap.ProcessFloodFill(testX, testY, "test_user")
			
			fmt.Printf("连锁展开结果:\n")
			fmt.Printf("触发位置: (%d, %d)\n", result.TriggerX, result.TriggerY)
			fmt.Printf("揭示方块数: %d\n", result.TotalRevealed)
			
			if result.TotalRevealed > 0 {
				fmt.Println("揭示的方块详情:")
				for i, revealedBlock := range result.RevealedBlocks {
					fmt.Printf("%d. 位置:(%d,%d) 周围地雷数:%d\n", 
						i+1, revealedBlock.X, revealedBlock.Y, revealedBlock.NeighborMines)
				}
			}
		}
	} else {
		fmt.Println("没有找到空白格，无法测试连锁展开")
	}
	
	fmt.Println("\n=== 坐标系统验证完成 ===")
	fmt.Println("新坐标系统特点:")
	fmt.Println("- 左下角为(0,0)")
	fmt.Println("- 右下角为(7,0)")
	fmt.Println("- 左上角为(0,7)")
	fmt.Println("- 右上角为(7,7)")
	fmt.Println("- Y轴数值越大表示越靠上")
}
