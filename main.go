package main

import (
	"context"
	"encoding/json"
	"minesweep/common/acceptor"
	"minesweep/common/logx"
	"minesweep/common/redisx"
	"minesweep/common/safe"
	"minesweep/conf"
	"minesweep/consumer"
	"minesweep/cron"
	"minesweep/initialize"
	"minesweep/invitemgr"
	"minesweep/model/common/base"
	"minesweep/model/dao"
	"minesweep/pairmgr"
	"minesweep/persistmgr"
	"minesweep/roommgr"
	"minesweep/voiceroom"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	logx.Init(logx.Conf{Path: "./logs/minesweep.log", Encoder: "console"})
	defer logx.Sync()

	if err := initialize.Init(); err != nil {
		logx.Errorf("initialize Init err:%v", err)
		return
	}

	// 启动房间管理主动逻辑
	roommgr.GetInstance().Start()

	// 启动语聊房管理的主动逻辑
	voiceroom.GetInstance().Start()

	// 启动添加闲家机器人
	pairmgr.GetInstance().Start()

	// 启动邀请管理的主动逻辑
	invitemgr.GetInstance().Start()

	// 启动数据持久化
	persistmgr.GetInstance().Start()

	// 启动消费者
	consumer.GetInstance().Start()

	// 启动定时任务
	cron.GetInstance().Start()

	// 监听游戏退出
	safe.Go(shutdown)

	// 启动服务器监听
	acceptor.Init().Start()
}

// 监听游戏退出
func shutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGKILL, syscall.SIGQUIT, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logx.Info("Shutdown Server begin")

	// 通知连接服务器，关闭游戏服务器
	bytes, _ := json.Marshal(&base.Shutdown{
		SrvID: conf.Conf.Server.ID,
	})
	redisx.GetClient().Publish(context.TODO(), dao.ShutdownKey(), string(bytes))

	// 退出房间管理
	roommgr.GetInstance().Stop()

	// 退出语聊房间管理
	voiceroom.GetInstance().Stop()

	// 退出添加闲家机器人
	pairmgr.GetInstance().Stop()

	// 退出添加闲家机器人
	invitemgr.GetInstance().Stop()

	// 退出数据持久化
	persistmgr.GetInstance().Stop()

	// 退出消费者
	consumer.GetInstance().Stop()

	// 退出定时任务
	cron.GetInstance().Stop()

	logx.Info("Shutdown Server end")
	os.Exit(0)
}
