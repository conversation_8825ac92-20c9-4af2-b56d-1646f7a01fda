package main

import (
	"connect/common/acceptor"
	"connect/common/logx"
	"connect/common/safe"
	"connect/consumer"
	"connect/gameservice"
	"connect/initialize"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	logx.Init(logx.Conf{Path: "./logs/connect.log", Encoder: "console"})
	defer logx.Sync()

	if err := initialize.Init(); err != nil {
		logx.Errorf("initialize Init err:%v", err)
		return
	}

	// 启动消费者
	consumer.GetInstance().Start()

	// 启动游戏服务检查
	gameservice.GetInstance().Start()

	// 监听游戏退出
	safe.Go(shutdown)

	// 启动服务器监听
	acceptor.Init().Start()
}

// 监听游戏退出
func shutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGKILL, syscall.SIGQUIT, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 退出消费者
	consumer.GetInstance().Stop()

	// 退出游戏服务检查
	gameservice.GetInstance().Stop()

	logx.Info("Shutdown Server end")
	os.Exit(0)
}
