//go:build dev
// +build dev

// 编译约束，执行go build -tags dev，编译该文件

package platform

// 压力测试时，不使用平台的账号信息

import (
	"fmt"
	"minesweep/common/logx"
	"ms-version.soofun.online/wjl/game_public/external"
	"sync"
)

var allPlayer sync.Map // userId <---> *external.ZegoUserInfo
var robotId int

// 获取一个机器人id
func getRobotId() int {
	robotId++
	return robotId
}

// 获取玩家信息
func GetUserInfo(param UserInfoReq) (external.ZegoUserInfo, error, int) {
	userInfo := external.ZegoUserInfo{
		UserID:   param.UserID,
		Coin:     1000000,
		Nickname: fmt.Sprintf("Nick_%v", param.UserID),
		Avatar:   "http://5b0988e595225.cdn.sohucs.com/images/20180625/cb1ecabde75942359718b0aca174594f.jpeg",
		SSToken:  fmt.Sprintf("Token_%v", param.UserID),
	}

	allPlayer.Store(param.UserID, &userInfo)

	logx.Infof("Platform:GetUserInfo userId=%v Coin=%v", userInfo.UserID, userInfo.Coin)

	return userInfo, nil, 0
}

// 修改玩家的资产
func ChangeBalance(appId int64, userId string, ssToken string, appChannel string, ActionEvent int, Amount int,
	RoomID string, gameRoundId string, extend string) (*external.ZegoBalanceRes, error) {
	balanceRes := external.ZegoBalanceRes{
		Code:       0,
		Msg:        "Ok",
		NewBalance: 0,
	}

	value, ok := allPlayer.Load(userId)
	if !ok {
		balanceRes.Code = 1008 // 资产不够
		return &balanceRes, fmt.Errorf("资产不够")
	} else {
		userInfo := value.(*external.ZegoUserInfo)
		if userInfo.Coin+int64(Amount) < 0 {
			balanceRes.Code = 1008 // 资产不够
			return &balanceRes, fmt.Errorf("资产不够")
		}

		userInfo.Coin += int64(Amount)
		balanceRes.NewBalance = int(userInfo.Coin)
		logx.Infof("Platform:ChangeBalance userId=%v Coin=%v Amount=%v", userInfo.UserID, userInfo.Coin, Amount)
	}

	return &balanceRes, nil
}

func ChangeBalanceByType(appId int64, userId string, ssToken string, appChannel string, ActionEvent int, Amount int,
	RoomID string, gameRoundId string, extend string, msgType string, balanceType int) (*external.ZegoBalanceRes, error) {
	balanceRes := external.ZegoBalanceRes{
		Code:       0,
		Msg:        "Ok",
		NewBalance: 0,
	}

	value, ok := allPlayer.Load(userId)
	if !ok {
		balanceRes.Code = 1008 // 资产不够
		return &balanceRes, fmt.Errorf("资产不够")
	} else {
		userInfo := value.(*external.ZegoUserInfo)
		if userInfo.Coin+int64(Amount) < 0 {
			balanceRes.Code = 1008 // 资产不够
			return &balanceRes, fmt.Errorf("资产不够")
		}

		userInfo.Coin += int64(Amount)
		balanceRes.NewBalance = int(userInfo.Coin)
		logx.Infof("Platform:ChangeBalance userId=%v Coin=%v Amount=%v", userInfo.UserID, userInfo.Coin, Amount)
	}

	return &balanceRes, nil
}

func ChangeRobotBalance(appId int64, userId string, ssToken string, appChannel string, ActionEvent int, Amount int,
	RoomID string, gameRoundId string, extend string) (*external.ZegoBalanceRes, error) {
	balanceRes := external.ZegoBalanceRes{
		Code:       0,
		Msg:        "Ok",
		NewBalance: 0,
	}

	value, ok := allPlayer.Load(userId)
	if !ok {
		balanceRes.Code = 1008 // 资产不够
		return &balanceRes, fmt.Errorf("资产不够")
	} else {
		userInfo := value.(*external.ZegoUserInfo)
		if userInfo.Coin+int64(Amount) < 0 {
			balanceRes.Code = 1008 // 资产不够
			return &balanceRes, fmt.Errorf("资产不够")
		}

		userInfo.Coin += int64(Amount)
		balanceRes.NewBalance = int(userInfo.Coin)
		logx.Infof("Platform:ChangeBalance userId=%v Coin=%v Amount=%v", userInfo.UserID, userInfo.Coin, Amount)
	}

	return &balanceRes, nil
}

// 获取玩家的资产  GetBalanceInfo
func GetBalanceInfo(UserID string, AppID int64, AppChannel string, SSToken string, ClientIP string) (*external.ZegoBalanceChangeRes, error) {
	value, ok := allPlayer.Load(UserID)
	if !ok {
		return nil, fmt.Errorf("没有找到玩家")
	}
	balanceRes := external.ZegoBalanceChangeRes{
		Code:     0,
		Msg:      "Ok",
		UniqueID: "UniqueID",
		Data:     external.ZegoBalanceInfo{CurrencyBalance: value.(*external.ZegoUserInfo).Coin},
	}
	return &balanceRes, nil
}

// 上报玩家状态
func UpdateUserStatus(appChannel string, appId int64, userId string, sstoken string, gameMode int, userRoomId string, roundId string,
	startAt int64, endAt int64, isLooker bool) {

}

// 获取机器人
func GetRandRobot(appChannel string, appId int64, count int) []Robot {
	robotArray := make([]Robot, 0)
	for i := 0; i < count; i++ {
		robotId := getRobotId()
		robot := Robot{
			Avatar:   "",
			Gender:   1,
			Nickname: fmt.Sprintf("Robot_%v", robotId),
			UserID:   fmt.Sprintf("%v", robotId),
		}
		robotArray = append(robotArray, robot)
	}

	return robotArray
}
