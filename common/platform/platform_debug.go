package platform

import (
	"fmt"
	"minesweep/common/logx"
	"ms-version.soofun.online/wjl/game_public/external"
	"sync"
)

var allPlayer sync.Map // userId <---> *external.ZegoUserInfo
var robotId int

// getRobotId 获取一个机器人id
func getRobotId() int {
	robotId++
	return robotId
}

// CacheUserInfo 缓存debug玩家信息
func CacheUserInfo(userID string, coin int64, appChannel string) {
	if appChannel != AppChannelDebug {
		return
	}
	userInfo := external.ZegoUserInfo{
		UserID:   userID,
		Coin:     coin,
		Nickname: fmt.Sprintf("Nick_%v", userID),
		Avatar:   "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg",
		SSToken:  fmt.Sprintf("Token_%v", userID),
	}
	allPlayer.Store(userID, &userInfo)
}

// GetUserInfo2 获取玩家信息
func GetUserInfo2(param UserInfoReq) (external.ZegoUserInfo, error, int) {
	userInfo := external.ZegoUserInfo{
		UserID:   param.UserID,
		Coin:     50000,
		Nickname: fmt.Sprintf("Nick_%v", param.UserID),
		Avatar:   "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg",
		SSToken:  fmt.Sprintf("Token_%v", param.UserID),
	}
	allPlayer.Store(param.UserID, &userInfo)

	logx.Infof("Platform:GetUserInfo userId=%v Coin=%v", userInfo.UserID, userInfo.Coin)
	return userInfo, nil, 0
}

// ChangeBalance2 修改玩家的资产
func ChangeBalance2(appId int64, userId string, ssToken string, appChannel string, ActionEvent int, Amount int,
	RoomID string, gameRoundId string, extend string) (*external.ZegoBalanceRes, error) {
	balanceRes := external.ZegoBalanceRes{
		Code:       0,
		Msg:        "Ok",
		NewBalance: 0,
	}

	value, ok := allPlayer.Load(userId)
	if !ok {
		balanceRes.Code = 1008 // 资产不够
		return &balanceRes, fmt.Errorf("资产不够")
	}

	userInfo := value.(*external.ZegoUserInfo)
	if userInfo.Coin+int64(Amount) < 0 {
		balanceRes.Code = 1008 // 资产不够
		return &balanceRes, fmt.Errorf("资产不够")
	}

	userInfo.Coin += int64(Amount)
	balanceRes.NewBalance = int(userInfo.Coin)
	logx.Infof("Platform:ChangeBalance userId=%v Coin=%v Amount=%v", userInfo.UserID, userInfo.Coin, Amount)

	return &balanceRes, nil
}

func ChangeBalanceByType2(appId int64, userId string, ssToken string, appChannel string, ActionEvent int, Amount int,
	RoomID string, gameRoundId string, extend string, msgType string, balanceType int) (*external.ZegoBalanceRes, error) {
	balanceRes := external.ZegoBalanceRes{
		Code:       0,
		Msg:        "Ok",
		NewBalance: 0,
	}

	value, ok := allPlayer.Load(userId)
	if !ok {
		balanceRes.Code = 1008 // 资产不够
		return &balanceRes, fmt.Errorf("资产不够")
	}

	userInfo := value.(*external.ZegoUserInfo)
	if userInfo.Coin+int64(Amount) < 0 {
		balanceRes.Code = 1008 // 资产不够
		return &balanceRes, fmt.Errorf("资产不够")
	}

	userInfo.Coin += int64(Amount)
	balanceRes.NewBalance = int(userInfo.Coin)
	logx.Infof("Platform:ChangeBalance userId=%v Coin=%v Amount=%v", userInfo.UserID, userInfo.Coin, Amount)

	return &balanceRes, nil
}

// ChangeRobotBalance2 上报机器人的结算信息
func ChangeRobotBalance2(appId int64, userId string, ssToken string, appChannel string, ActionEvent int, Amount int,
	RoomID string, gameRoundId string, extend string) (*external.ZegoBalanceRes, error) {
	balanceRes := external.ZegoBalanceRes{
		Code:       0,
		Msg:        "Ok",
		NewBalance: 0,
	}

	value, ok := allPlayer.Load(userId)
	if !ok {
		balanceRes.Code = 1008 // 资产不够
		return &balanceRes, fmt.Errorf("资产不够")
	}

	userInfo := value.(*external.ZegoUserInfo)
	if userInfo.Coin+int64(Amount) < 0 {
		balanceRes.Code = 1008 // 资产不够
		return &balanceRes, fmt.Errorf("资产不够")
	}

	userInfo.Coin += int64(Amount)
	balanceRes.NewBalance = int(userInfo.Coin)
	logx.Infof("Platform:ChangeBalance userId=%v Coin=%v Amount=%v", userInfo.UserID, userInfo.Coin, Amount)

	return &balanceRes, nil
}

// GetBalanceInfo2 获取玩家的资产
func GetBalanceInfo2(UserID string, AppID int64, AppChannel string, SSToken string, ClientIP string) (*external.ZegoBalanceChangeRes, error) {
	value, ok := allPlayer.Load(UserID)
	if !ok {
		return nil, fmt.Errorf("没有找到玩家")
	}

	balanceRes := external.ZegoBalanceChangeRes{
		Code:     0,
		Msg:      "Ok",
		UniqueID: "UniqueID",
		Data:     external.ZegoBalanceInfo{CurrencyBalance: value.(*external.ZegoUserInfo).Coin},
	}
	return &balanceRes, nil
}

// GetRandRobot2 获取机器人
func GetRandRobot2(appChannel string, appId int64, count int) []Robot {
	robotArray := make([]Robot, 0)
	for i := 0; i < count; i++ {
		id := getRobotId()
		robotArray = append(robotArray, Robot{
			Avatar:   "http://5b0988e595225.cdn.sohucs.com/images/20180625/cb1ecabde75942359718b0aca174594f.jpeg",
			Gender:   1,
			Nickname: fmt.Sprintf("Robot_%v", id),
			UserID:   fmt.Sprintf("%v", id),
		})
	}
	return robotArray
}
