package msg

type ToClientMsg struct {
	MsgID string `json:"msgId"`
	Code  int    `json:"code"`
	Msg   string `json:"msg"`
	Data  any    `json:"data"`
}

type FromClientMsg struct {
	MsgID string `json:"msgId"`
	Data  any    `json:"data"`
}

type ClientMsg[T any] struct {
	MsgID string `json:"msgId"`
	Data  T      `json:"data"`
}

type NotifyClientMsg struct {
	MsgID string `json:"msgId"`
	Data  any    `json:"data"`
}
