package acceptor

import (
	"fmt"
	"minesweep/api"
	"minesweep/common/idgen"
	"minesweep/common/iface"
	"minesweep/common/logx"
	"minesweep/common/msg"
	"minesweep/common/websocketx"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/middleware"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type Acceptor struct {
	addr    string // 接收器监听地址
	httpSvr *http.Server

	acceptorHandle iface.IAcceptorHandle // acceptor处理
	socketMap      sync.Map              // netID-->WsSocket
}

var acceptor *Acceptor

func GetAcceptor() iface.IAcceptor {
	return acceptor
}

func Init() iface.IAcceptor {
	return &Acceptor{
		addr:           "0.0.0.0:" + conf.Conf.Server.Port,
		acceptorHandle: nil,
	}
}

func (w *Acceptor) GetAddr() string {
	return w.addr
}

// Start 启动服务器监听
func (w *Acceptor) Start() {
	w.httpSvr = &http.Server{
		Addr:         w.addr,
		Handler:      w.routers(),
		ReadTimeout:  20 * time.Second,
		WriteTimeout: 20 * time.Second,
	}

	// 启动服务
	logx.Info("Run ListenAndServe ...")
	if err := w.httpSvr.ListenAndServe(); err != nil {
		logx.Errorf("Run ListenAndServe err:%v", err)
	}
}

// routers http路由初始化
func (w *Acceptor) routers() *gin.Engine {
	routers := gin.Default()
	routers.Use(middleware.Cors())

	// 服务健康检测
	routers.OPTIONS(fmt.Sprintf("/%v/game_route", conf.Conf.Server.RoutePrefix), func(context *gin.Context) {
		context.String(http.StatusOK, "ok")
	})

	// http接口处理
	api.InitRouter(routers.Group(conf.Conf.Server.RoutePrefix))

	// 临时(已在api.InitRouter处理，不确定路由的前缀是否为配置文件的RoutePrefix)
	//routers.POST(fmt.Sprintf("/%v/query_voice_room", "minesweep_yuliao"), new(api.SystemApi).QueryVoiceRoom)

	// websocket长链接
	routers.GET("/acceptor", w.wsWorker)

	return routers
}

// wsWorker 处理长链接
func (w *Acceptor) wsWorker(ctx *gin.Context) {
	if !websocket.IsWebSocketUpgrade(ctx.Request) {
		logx.Errorf("UpgradeToWebsocket RemoteIP:%v 不是websocket链接", ctx.RemoteIP())
		return
	}

	var upGrader = websocket.Upgrader{
		ReadBufferSize:   1024,
		WriteBufferSize:  1024,
		HandshakeTimeout: 5 * time.Second,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
		Subprotocols: []string{ctx.GetHeader("Sec-WebSocket-Protocol")},
	}
	conn, err := upGrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		logx.Errorf("UpgradeToWebsocket Upgrade err:%v", err)
		return
	}

	logx.Infof("Url==>%v", ctx.Request.URL.String())

	params := make(map[string]string)
	params["env"] = ctx.Query("env")                 // 环境 0-开发 1-测试 2-正式(无用)
	params["country"] = ctx.Query("country")         // 国家(无用)
	params["game_id"] = ctx.Query("game_id")         // 游戏ID
	params["game_mode"] = ctx.Query("game_mode")     // 游戏模式 2-半屏 3-全屏
	params["user_id"] = ctx.Query("user_id")         // 用户ID
	params["code"] = ctx.Query("code")               // 用户唯一标识
	params["app_id"] = ctx.Query("app_id")           // 应用ID
	params["app_channel"] = ctx.Query("app_channel") // 应用渠道ID
	params["room_id"] = ctx.Query("room_id")         // 房间ID
	params["role"] = ctx.Query("role")               // 玩家角色 1-游客 其他-玩家
	params["clientIp"] = ctx.ClientIP()              // 获取客户端IP

	// 检测是否重复登录(该用户ID已有旧链接存在)
	if oldNetId := w.acceptorHandle.CheckRepeatLogin(params); oldNetId != 0 {
		w.RemoveSocket(oldNetId)
	}

	// 网络ID需要全局生成
	sock := websocketx.Init(conn)
	sock.SetNetID(idgen.GetNewId())

	// 校验登录参数
	errCode := w.acceptorHandle.CheckAllowConnect(params, sock)
	if errCode == ecode.OK {
		// 放入管理器中
		w.socketMap.Store(sock.GetNetID(), sock)

		// 创建长连接成功
		_ = sock.WriteMessage(&msg.ToClientMsg{
			MsgID: constvar.MsgTypeCreateWs,
			Code:  ecode.OK,
			Msg:   "success",
			Data:  map[string]interface{}{},
		})

		// 启动接收线程
		w.recvClientMsg(sock)
	} else {
		logx.Infof("CheckAllowConnect failed, errCode:%v, params:%+v", errCode, params)
		// 禁止连接的话，返回错误码
		_ = sock.WriteMessage(&msg.ToClientMsg{
			MsgID: constvar.MsgTypeCreateWs,
			Code:  errCode,
			Msg:   ecode.GetMsg(errCode),
		})

		// 禁止连接的话,通过心跳的消息格式给客户端返回
		//_ = sock.WriteMessage(&msg.ToClientMsg{
		//	MsgID:   "Heartbeat",
		//	Code: errCode,
		//	Msg:  ecode.GetMsg(errCode),
		//})
		// 去掉登录失败主动关闭连接，否则前端收不到这个心跳失败消息
		// sock.Close()
	}
}

// 接收消息线程
func (w *Acceptor) recvClientMsg(sock *websocketx.WsSocket) {
	defer func() {
		logx.Infof("recvClientMsg socket UserID:%v, NetId:%d close!", sock.GetUserID(), sock.GetNetID())
		sock.Close()
		w.acceptorHandle.OnClose(sock.GetNetID())
		w.socketMap.Delete(sock.GetNetID())
	}()

	for {
		data, err := sock.ReadMessage()
		if err != nil {
			//发送数据不正确, 直接删除连接
			logx.Infof("recvClientMsg ReadMessage err:%v", err)
			break
		}

		w.acceptorHandle.OnMessage(sock, data)
	}
}

// RemoveSocket 移除WsSocket
func (w *Acceptor) RemoveSocket(netID int) {
	v, ok := w.socketMap.Load(netID)
	if ok {
		s := v.(*websocketx.WsSocket)
		s.Close()
		w.socketMap.Delete(netID)
		w.acceptorHandle.OnClose(netID)
	}
}
