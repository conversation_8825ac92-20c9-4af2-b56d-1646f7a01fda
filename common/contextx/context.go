package contextx

import (
	"connect/common/logx"
	"connect/ecode"
	"connect/model/common/response"
	"github.com/gin-gonic/gin"
	"net/http"
)

type Context struct {
	ctx       *gin.Context
	paramsMap map[string]interface{}
}

func NewContext(ctx *gin.Context, params interface{}) (r *Context, isAllow bool) {
	r = &Context{
		ctx: ctx,
	}
	if r.ctx.Request.Method == "OPTIONS" {
		r.ctx.String(http.StatusOK, "")
		return
	}

	defer func() {
		query := r.ctx.Request.URL.RawQuery
		if query != "" {
			query = "?" + query
		}
		urlPath := r.ctx.Request.URL.Path
		logx.Infof("%s | %s %s | %+v", ctx.ClientIP(), r.ctx.Request.Method, urlPath+query, params)
	}()

	// validate params
	if params != nil {
		if err := r.ctx.ShouldBind(params); err != nil {
			response.Fail(ecode.ErrParams, ctx)
			return
		}
	}
	isAllow = true
	return
}
