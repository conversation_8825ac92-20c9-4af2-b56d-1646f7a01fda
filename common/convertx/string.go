package convertx

import (
	"hash/crc32"
	"strconv"
)

func StringToInt(v string) int {
	i, _ := strconv.Atoi(v)
	return i
}

func IntToString(v int) string {
	return strconv.Itoa(v)
}

func Int64ToString(v int64) string {
	return strconv.FormatInt(v, 10)
}

func StringToInt64(v string) int64 {
	value, _ := strconv.ParseInt(v, 10, 64)
	return value
}

func BoolToInt(v bool) int {
	if v {
		return 1
	}
	return 0
}

func StringHashToInt(s string) int {
	hash := crc32.ChecksumIEEE([]byte(s)) // 生成 32 位哈希
	return int(hash)                      // 转换为 int
}
