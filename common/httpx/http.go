package httpx

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

func SendPost(_url string, _post interface{}) (int, []byte, error) {
	bytesData, err := json.Marshal(_post)
	if err != nil {
		return 0, nil, err
	}

	req, err := http.NewRequest("POST", _url, bytes.NewBuffer(bytesData))
	if err != nil {
		return 0, nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return 0, nil, err
	}
	defer resp.Body.Close()

	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 0, nil, err
	}
	return resp.StatusCode, respBytes, nil
}

func SendGet(_url string) (int, []byte, error) {
	req, err := http.NewRequest("GET", _url, nil)
	if err != nil {
		return 0, nil, err
	}
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return 0, nil, err
	}
	defer resp.Body.Close()

	respBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return 0, nil, err
	}
	return resp.StatusCode, respBytes, nil
}

func UrlPathJoin(part ...string) string {
	for i := 0; i < len(part); i++ {
		if i == 0 {
			part[i] = strings.TrimRight(part[i], "/")
		} else if i == len(part)-1 {
			part[i] = strings.TrimLeft(part[i], "/")
		} else {
			part[i] = strings.Trim(part[i], "/")
		}
	}
	return strings.Join(part, "/")
}
