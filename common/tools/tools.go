package tools

import (
	"connect/common/logx"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/rand"
	"net"
	"os"
	"strings"
	"time"
)

var safeRand = NewSafeRand()

// PrintObj 打印对象
func PrintObj(obj any, format string, a ...interface{}) {
	tagStr := fmt.Sprintf(format, a...)
	bytes, err := json.Marshal(obj)
	if err == nil {
		logx.Infof("%v ==> %v", tagStr, string(bytes))
	} else {
		logx.Errorf("打印 %v 信息失败：%v", tagStr, err.Error())
	}
}

// GetObj 获取对象
func GetObj(obj any) string {
	bytes, _ := json.Marshal(obj)
	return string(bytes)
}

// TimeCost 打印调用消耗
func TimeCost() func(flag string) {
	start := time.Now()
	return func(flag string) {
		tc := time.Since(start).Milliseconds()
		logx.Infof("%s time cost = %v Millisecond", flag, tc)
	}
}

// GetInternalIP 返回本机IP
func GetInternalIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		logx.Errorf("GetInternalIP() internal ip fetch failed, error : %s", err)
	}

	ip := conn.LocalAddr().String()
	ip = strings.Split(ip, ":")[0]
	_ = conn.Close()
	return ip
}

// LotteryDraw 抽奖
func LotteryDraw(probability []int) int {
	var upperLimits []int
	var upperLimit int
	for _, v := range probability {
		upperLimit += v
		upperLimits = append(upperLimits, upperLimit)
	}

	var index int
	var randNum = RandNum(upperLimit)
	for k, v := range upperLimits {
		if randNum < v {
			index = k
			break
		}
	}
	return index
}

// Rand 返回闭区间 [min,max]
func Rand(min, max int) int {
	if min == max {
		return min
	}
	return RandNum(max-min+1) + min
}

// RandNum 生成随机数[0, count-1]
func RandNum(count int) int {
	return int(safeRand.Int63() % int64(count))
}

// PathExists 判断文件是否存在
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	//IsNotExist来判断，是不是不存在的错误
	//如果返回的错误类型使用os.isNotExist()判断为true，说明文件或者文件夹不存在
	if os.IsNotExist(err) {
		return false, nil
	}
	//如果有错误了，但是不是不存在的错误，所以把这个错误原封不动的返回
	return false, err
}

// IsContain 判断 slice 中是否有指定的数据
func IsContain[T int | string](a []T, v T) bool {
	for _, t := range a {
		if t == v {
			return true
		}
	}

	return false
}

// CheckEvent 检测 chan 是否有数据
func CheckEvent(c chan int) bool {
	var rst bool
	select {
	case <-c:
		rst = true
	default:
		rst = false
	}
	return rst
}

// CheckClose 检测channel是否已经关闭,返回 true 表示已经关闭,false 未关闭
func CheckClose[T any](c chan T) bool {
	select {
	case _, received := <-c:
		if !received {
			return true
		}
	default:
	}

	return false
}

// Shuffle 洗牌
func Shuffle[T any](arr []T) {
	rand.Shuffle(len(arr), func(i, j int) {
		arr[i], arr[j] = arr[j], arr[i]
	})
}

// ShuffleInt 洗牌
func ShuffleInt(arr []int) {
	safeRand.Shuffle(arr)
}

// RandSlice 随机返回一个元素
func RandSlice[T any](arr []T) T {
	var t T
	count := len(arr)
	if count == 0 {
		return t
	}
	if count == 1 {
		return arr[0]
	}

	return arr[RandNum(count)]
}

// GenerateSignature ...
func GenerateSignature(signatureNonce string, serverSecret string, timestamp int64) (Signature string) {
	data := fmt.Sprintf("%s%s%d", signatureNonce, serverSecret, timestamp)
	h := md5.New()
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// ToString json to string
func ToString(obj any) string {
	bytes, err := json.Marshal(obj)
	if err != nil {
		return fmt.Sprintf("json 序列化失败,%v", err.Error())
	}
	return string(bytes)
}
