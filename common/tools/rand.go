package tools

import (
	"math/rand"
	"minesweep/common/logx"
	"sync"
	"time"
)

// SafeRand 在 Go 1.20 及以后版本中，推荐的做法是创建一个 rand.Rand 实例，并使用 rand.Seed 来初始化，如rng := rand.New(rand.NewSource(time.Now().UnixNano()))
// SafeRand 在并发程序中，最好为每个 goroutine 创建独立的 rand.Rand 实例，以避免线程安全问题
// SafeRand rand.Source 需要使用加锁保证线程安全
type SafeRand struct {
	sync.Mutex
	rand         *rand.Rand
	lastSeedTime time.Time // 最后一次更新随机种子的时间
}

func NewSafeRand() *SafeRand {
	return &SafeRand{
		rand:         rand.New(rand.NewSource(time.Now().UnixNano())), // 线程不安全, rand.NewSource也线程不安全
		lastSeedTime: time.Now(),
	}
}

func (s *SafeRand) Int63() int64 {
	s.Lock()
	defer s.Unlock()

	if time.Now().Sub(s.lastSeedTime) > time.Second*60*5 {
		s.lastSeedTime = time.Now()
		s.rand = rand.New(rand.NewSource(time.Now().UnixNano()))
		logx.Info("tools:Int63 更新随机种子")
	}
	return s.rand.Int63()
}

func (s *SafeRand) Shuffle(arr []int) {
	s.Lock()
	defer s.Unlock()

	s.rand.Shuffle(len(arr), func(i, j int) {
		arr[i], arr[j] = arr[j], arr[i]
	})
}
