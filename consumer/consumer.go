package consumer

import (
	"connect/common/safe"
	"github.com/json-iterator/go"
)

type Consumer struct {
	*Subscribe
	*GameAck
}

var consumer = &Consumer{
	Subscribe: NewSubscribe(),
	GameAck:   NewGameAck(),
}

func GetInstance() *Consumer {
	return consumer
}

func (slf *Consumer) Start() {
	safe.Go(slf.Subscribe.Start)
	safe.Go(slf.GameAck.Start)
}

func (slf *Consumer) Stop() {
	slf.GameAck.Stop()
}

var jsonIterator = jsoniter.ConfigCompatibleWithStandardLibrary
