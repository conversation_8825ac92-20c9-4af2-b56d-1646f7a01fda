package consumer

import (
	"minesweep/clientHandle"
	"minesweep/common/logx"
	"minesweep/model/common/base"
	"minesweep/model/dao"
	"sync"
)

type GameReq struct {
	stopCh chan int
	stopWg sync.WaitGroup
}

func NewGameReq() *GameReq {
	return &GameReq{stopCh: make(chan int)}
}

func (slf *GameReq) Start() {
	defer slf.stopWg.Done()
	slf.stopWg.Add(1)
	for {
		select {
		case <-slf.stopCh:
			return
		default:
		}

		value, err := dao.GroupDao.GameReqList.BLPop()
		if err != nil {
			continue
		}

		logx.Infof("OnGameReq Msg:%v", value[1])
		msg := &base.GameReqMsg{}
		err = jsonIterator.Unmarshal([]byte(value[1]), msg)
		if err != nil {
			logx.Errorf("OnGameReq Unmarshal err:%v", err)
			continue
		}

		field := clientHandle.GetHandler().Field(msg.AppChannel, msg.AppID, msg.UserID)
		ch, err := clientHandle.GetHandler().GetChan(field)
		if err != nil {
			logx.Errorf("OnGameAck GetChan err:%v, field:%v", err, field)
			continue
		}
		ch <- msg
	}
}

func (slf *GameReq) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}
