package cache

import (
	"minesweep/common/logx"
	"minesweep/model"
	"sync"
)

type TableNameMgr struct {
	sync.RWMutex
	tableNames map[string]struct{}
}

func NewTableMgr() *TableNameMgr {
	return &TableNameMgr{
		tableNames: make(map[string]struct{}),
	}
}

func (slf *TableNameMgr) Add(tableName string) {
	slf.Lock()
	defer slf.Unlock()

	slf.tableNames[tableName] = struct{}{}
}

func (slf *TableNameMgr) Remove(tableName string) {
	slf.Lock()
	defer slf.Unlock()

	delete(slf.tableNames, tableName)
}

func (slf *TableNameMgr) HasTable(tableName string) bool {
	slf.RLock()
	defer slf.RUnlock()

	if _, ok := slf.tableNames[tableName]; ok {
		return true
	}

	return false
}

func (slf *TableNameMgr) CreateGameRecordTable(tableName string, isCheck bool) error {
	if isCheck && slf.HasTable(tableName) {
		return nil
	}

	if err := model.NewGameRecordSearch().CreateTable(tableName); err != nil {
		logx.Errorf("CreateGameRecordTable tableName:%v, err:%v", tableName, err)
		return err
	}

	logx.Infof("CreateGameRecordTable tableName:%v", tableName)
	slf.Add(tableName)
	return nil
}
