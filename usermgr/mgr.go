package usermgr

import (
	"connect/common/convertx"
	"connect/common/logx"
	"connect/common/msg"
	"connect/conf"
	"connect/constvar"
	"connect/ecode"
	"connect/model/common/base"
	"connect/model/dao"
	"fmt"
	"runtime/debug"
	"slices"
	"stathat.com/c/consistent"
	"strings"
	"sync"
)

type UserManager struct {
	sync.RWMutex
	netMap     sync.Map                         // socketID -> *User
	userMap    sync.Map                         // userID -> *User
	voiceUsers map[string]map[string]bool       // platRoomID -> map[string]bool
	ackChs     map[string]chan *base.GameAckMsg // queueIndex -> chan *base.GameAckMsg
	consist    *consistent.Consistent
}

var instance = NewUserMgr()

func GetInstance() *UserManager {
	return instance
}

func NewUserMgr() *UserManager {
	ack := &UserManager{
		voiceUsers: make(map[string]map[string]bool),
		ackChs:     make(map[string]chan *base.GameAckMsg),
		consist:    consistent.New(),
	}
	for i := 1; i <= 10; i++ {
		ack.consist.Add(convertx.IntToString(i))
	}
	return ack
}

func (u *UserManager) Field(appChanel string, appID int64, id string) string {
	return fmt.Sprintf("%v,%v,%v", appChanel, appID, id)
}

func (u *UserManager) AddUser(user *User) {
	logx.Infof("UserJoin userID:%v, netID:%v", user.UserID, user.Socket.GetNetID())
	u.netMap.Store(user.Socket.GetNetID(), user)
	u.userMap.Store(u.Field(user.AppChannel, user.AppID, user.UserID), user)
	if conf.Conf.Server.GameId.IsVoice() && len(user.PlatRoomID) > 0 {
		u.AddVoiceUser(user)
	}
}

func (u *UserManager) AddVoiceUser(user *User) {
	u.Lock()
	defer u.Unlock()

	_, ok := u.voiceUsers[user.PlatRoomID]
	if !ok {
		u.voiceUsers[user.PlatRoomID] = make(map[string]bool)
	}
	u.voiceUsers[user.PlatRoomID][u.Field(user.AppChannel, user.AppID, user.UserID)] = true
}

func (u *UserManager) GetUser(socketID int) *User {
	value, ok := u.netMap.Load(socketID)
	if ok {
		return value.(*User)
	}
	return nil
}

func (u *UserManager) GetUserById(appChannel string, appID int64, userID string) *User {
	value, ok := u.userMap.Load(u.Field(appChannel, appID, userID))
	if ok {
		return value.(*User)
	}
	return nil
}

func (u *UserManager) RmvUser(appChannel string, appID int64, userID string, socketID int) {
	u.Lock()
	defer u.Unlock()

	logx.Infof("RmvUser userID:%v, NetID:%v", userID, socketID)
	user := u.GetUser(socketID)
	if user != nil && len(user.PlatRoomID) > 0 {
		userMap, ok := u.voiceUsers[user.PlatRoomID]
		if ok {
			delete(userMap, u.Field(user.AppChannel, user.AppID, user.UserID))
		}
	}
	u.netMap.Delete(socketID)
	u.userMap.Delete(u.Field(appChannel, appID, userID))
}

func (u *UserManager) NotifyTo(appChannel string, appID int64, userID string, data *msg.ToClientMsg) {
	user := u.GetUserById(appChannel, appID, userID)
	if user == nil {
		logx.Infof("NotifyTo no find userID:%v", userID)
		return
	}

	if data.Code == ecode.ErrNotFoundRoom {
		// 未找到房间，删除location
		dao.GroupDao.UserLocation.Delete(appChannel, appID, userID)
		user.UpdateLocation(&dao.UserLocation{}, "not found room")
	} else if data.MsgID == constvar.MsgTypeGameStart {
		// 游戏开始，更新location
		location, _ := dao.GroupDao.UserLocation.Get(appChannel, appID, userID)
		user.UpdateLocation(location, "game start")
	} else if data.MsgID == constvar.MsgTypeEnterRoom {
		// 进入房间，更新location
		location, _ := dao.GroupDao.UserLocation.Get(appChannel, appID, userID)
		user.UpdateLocation(location, "enter room")
	} else if data.Code == ecode.ErrNotFoundVoiceRoom {
		// 未找到语聊房
		user.UpdateVoiceInfo(&dao.VoiceInfo{}, "not found voice room")
	} else if data.MsgID == constvar.MsgTypeEnterVoiceRoom {
		voiceInfo, _ := dao.GroupDao.VoiceInfo.Get(user.AppChannel, user.AppID, user.PlatRoomID)
		user.UpdateVoiceInfo(voiceInfo, "enter voice room")
	} else if data.MsgID == constvar.MsgTypeSettlement {
		// 游戏结算，删除location
		dao.GroupDao.UserLocation.Delete(appChannel, appID, userID)
		user.UpdateLocation(&dao.UserLocation{}, "settle")
	} else if data.Code == ecode.ErrNotFoundUser {
		// 未找到用户，删除location(消息流程不对，没走EnterRoom就发游戏中消息)
		dao.GroupDao.UserLocation.Delete(appChannel, appID, userID)
		user.UpdateLocation(&dao.UserLocation{}, "not found user")
	}
	user.SendMessage(data)
}

func (u *UserManager) GetVoiceUsers(data *base.GameAckMsg) []string {
	u.RLock()
	defer u.RUnlock()

	var fieldList []string
	fields, ok := u.voiceUsers[data.PlatRoomID]
	if !ok {
		return fieldList
	}

	for field := range fields {
		fieldList = append(fieldList, field)
	}
	return fieldList
}

func (u *UserManager) VoiceBroad(data *base.GameAckMsg) {
	fieldList := u.GetVoiceUsers(data)
	for _, field := range fieldList {
		splits := strings.Split(field, ",")
		if len(splits) != 3 {
			logx.Infof("VoiceBroad Split err, field:%v", field)
			continue
		}

		if len(data.ExpectIDs) > 0 &&
			slices.Contains(data.ExpectIDs, splits[2]) {
			continue
		}

		if splits[2] == data.ExpectID {
			continue
		}

		_, ch, err := u.GetChan(field)
		if err != nil {
			continue
		}

		ch <- &base.GameAckMsg{
			AppChannel: data.AppChannel,
			AppID:      data.AppID,
			UserID:     splits[2],
			Data:       data.Data,
		}
	}
}

func (u *UserManager) GetChan(field string) (string, chan *base.GameAckMsg, error) {
	queue, err := u.consist.Get(field)
	if err != nil {
		logx.Infof("GetChan field:%v, err:%v", field, err)
		return "", nil, err
	}

	u.RLock()
	ch, ok := u.ackChs[queue]
	u.RUnlock()
	if !ok {
		ch = make(chan *base.GameAckMsg, 10000)
		u.Lock()
		u.ackChs[queue] = ch
		u.Unlock()
		go u.handleAck(queue, u.ackChs[queue])
	}
	return queue, ch, nil
}

func (u *UserManager) handleAck(queue string, ch chan *base.GameAckMsg) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("handleAck queue:%v, panicErr:%v", queue, err)
			debug.PrintStack()
			go u.handleAck(queue, ch)
		}
	}()

	for {
		select {
		case data := <-ch:
			if len(data.PlatRoomID) > 0 && len(data.UserID) == 0 {
				u.VoiceBroad(data)
				continue
			}

			u.NotifyTo(data.AppChannel, data.AppID, data.UserID, &data.Data)
		}
	}
}

// CloseByGameSrv 断开某游戏服务器的长连接
func (u *UserManager) CloseByGameSrv(gameSrvID string) {
	u.netMap.Range(func(key, value any) bool {
		user := value.(*User)
		if user != nil && (user.SrvID == gameSrvID || user.VoiceSrvID == gameSrvID) {
			logx.Infof("CloseByGameSrv userID:%v user.SrvID:%v, user.VoiceSrvID:%v", user.UserID, user.SrvID, user.VoiceSrvID)
			user.Socket.Close()
		}
		return true
	})
}

// CloseByVoice 断开某语聊房的长连接
func (u *UserManager) CloseByVoice(appChannel string, appID int64, platRoomID string) {
	u.netMap.Range(func(key, value any) bool {
		user := value.(*User)
		if user != nil &&
			user.AppChannel == appChannel &&
			user.AppID == appID &&
			user.PlatRoomID == platRoomID {
			logx.Infof("CloseByVoice userID:%v user.PlatRoomID:%v", user.UserID, user.PlatRoomID)
			user.Socket.Close()
		}
		return true
	})
}
