package usermgr

import (
	"connect/common/logx"
	"connect/common/platform"
	"connect/common/websocketx"
	"connect/constvar"
	"connect/ecode"
	"connect/model/common/base"
	"connect/model/dao"
	"sync"
)

type (
	User struct {
		sync.RWMutex
		Socket       *websocketx.WsSocket // 玩家的Socket
		AppID        int64                // appID
		AppChannel   string               // 平台渠道
		UserID       string               // 用户ID
		Nickname     string               // 昵称
		Avatar       string               // 头像
		Coin         int64                // 当前金币
		IsVisitor    bool                 // 是否是游客
		Code         string               // h5前端传的code(仅登录获取平台玩家信息有用)
		SSToken      string               // platform登录成功返回的Token
		GameMode     int                  // 游戏模式 2-半屏 3-全屏
		ClientIP     string               // 玩家的IP地址
		PlatRoomID   string               // 客户端传递过来的平台RoomID
		Role         constvar.Role        // 玩家角色 0-普通玩家 1-游客 2-管理员
		SrvID        string               // 游戏服务器ID
		RoomID       int64                // 游戏房间ID
		VoiceSrvID   string               // 语聊房游戏服务器ID
		LanguageCode string               // 语言码
	}
)

func (u *User) SendMessage(msgObj any) {
	if u.Socket != nil {
		_ = u.Socket.WriteMessage(msgObj)
	}
}

// UpdateBalance 更新玩家资产
func (u *User) UpdateBalance() int {
	resp, err := platform.GetBalanceInfo(u.UserID, u.AppID, u.AppChannel, u.SSToken, u.ClientIP)
	if err != nil {
		return ecode.ErrRequestUser
	}
	if resp.Code != 0 {
		if resp.Code >= 1001 && resp.Code <= 1018 {
			return ecode.ErrRequestUser
		}
		return int(resp.Code)
	}

	u.Coin = resp.Data.CurrencyBalance
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(u.AppChannel, u.AppID)
	if channelCfg.CoinType != 0 {
		for _, balanceList := range resp.Data.BalanceList {
			if balanceList.CurrencyType == channelCfg.CoinType {
				u.Coin = int64(balanceList.CurrencyAmount)
			}
		}
	}
	return ecode.OK
}

// ChangeRole 改变玩家角色
func (u *User) ChangeRole(newRole constvar.Role) {
	logx.Infof("ChangeRole userID:%v, oldRole:%v, newRole:%v", u.UserID, u.Role, newRole)
	u.Role = newRole
}

// UpdateLocation 更新玩家location
func (u *User) UpdateLocation(location *dao.UserLocation, reason string) {
	u.SrvID = location.SrvID
	u.RoomID = location.RoomID
	logx.Infof("UpdateLocation userID:%v, srvID:%v, roomID:%v, reason:%v", u.UserID, u.SrvID, u.RoomID, reason)
}

// UpdateVoiceInfo 更新语聊房信息
func (u *User) UpdateVoiceInfo(voiceInfo *dao.VoiceInfo, reason string) {
	u.VoiceSrvID = voiceInfo.SrvID
	logx.Infof("UpdateVoiceInfo userID:%v, voiceSrvID:%v, reason:%v", u.UserID, u.VoiceSrvID, reason)
}

// GetGameUser 获取用户信息
func (u *User) GetGameUser() *base.GameUser {
	return &base.GameUser{
		Nickname:     u.Nickname,
		Avatar:       u.Avatar,
		Coin:         u.Coin,
		SSToken:      u.SSToken,
		IsVisitor:    u.IsVisitor,
		GameMode:     u.GameMode,
		ClientIP:     u.ClientIP,
		Role:         u.Role,
		LanguageCode: u.LanguageCode,
	}
}
