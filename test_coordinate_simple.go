package main

import (
	"fmt"
	"math/rand"
	"time"
)

// 简化的测试结构，验证新坐标系统
type TestBlock struct {
	X             int
	Y             int
	IsMine        bool
	NeighborMines int
}

type TestMap struct {
	Width  int
	Height int
	Blocks [][]TestBlock
}

func NewTestMap() *TestMap {
	const (
		width  = 8
		height = 8
		mineCount = 13
	)

	m := &TestMap{
		Width:  width,
		Height: height,
		Blocks: make([][]TestBlock, height),
	}

	// 初始化地图块（新坐标系统：左下角为(0,0)，Y轴从下往上）
	for arrayY := 0; arrayY < height; arrayY++ {
		m.Blocks[arrayY] = make([]TestBlock, width)
		for x := 0; x < width; x++ {
			// 将数组索引转换为逻辑坐标：arrayY=0对应logicY=7，arrayY=7对应logicY=0
			logicY := height - 1 - arrayY
			m.Blocks[arrayY][x] = TestBlock{
				X:             x,
				Y:             logicY, // 存储逻辑Y坐标
				IsMine:        false,
				NeighborMines: 0,
			}
		}
	}

	// 生成地雷分布
	m.generateMines(mineCount)
	// 计算邻居地雷数
	m.calculateNeighborMines()

	return m
}

func (m *TestMap) generateMines(mineCount int) {
	rand.Seed(time.Now().UnixNano())

	totalPositions := m.Width * m.Height
	positions := make([]int, totalPositions)
	for i := 0; i < totalPositions; i++ {
		positions[i] = i
	}

	rand.Shuffle(totalPositions, func(i, j int) {
		positions[i], positions[j] = positions[j], positions[i]
	})

	minePositions := positions[:mineCount]

	for _, pos := range minePositions {
		x := pos % m.Width
		logicY := pos / m.Width
		// 将逻辑Y坐标转换为数组索引
		arrayY := m.Height - 1 - logicY
		m.Blocks[arrayY][x].IsMine = true
	}
}

func (m *TestMap) calculateNeighborMines() {
	// 8个方向的偏移量（新坐标系统：Y轴从下往上）
	directions := [][]int{
		{-1, 1}, {0, 1}, {1, 1},   // 上方三个（Y+1）
		{-1, 0},         {1, 0},   // 左右两个
		{-1, -1}, {0, -1}, {1, -1}, // 下方三个（Y-1）
	}

	for arrayY := 0; arrayY < m.Height; arrayY++ {
		for x := 0; x < m.Width; x++ {
			if m.Blocks[arrayY][x].IsMine {
				continue
			}

			// 获取当前格子的逻辑坐标
			currentLogicY := m.Height - 1 - arrayY

			mineCount := 0
			for _, dir := range directions {
				neighborLogicX := x + dir[0]
				neighborLogicY := currentLogicY + dir[1]
				
				if neighborLogicX >= 0 && neighborLogicX < m.Width && 
				   neighborLogicY >= 0 && neighborLogicY < m.Height {
					neighborArrayY := m.Height - 1 - neighborLogicY
					if m.Blocks[neighborArrayY][neighborLogicX].IsMine {
						mineCount++
					}
				}
			}
			m.Blocks[arrayY][x].NeighborMines = mineCount
		}
	}
}

func (m *TestMap) GetBlock(x, y int) *TestBlock {
	if x < 0 || x >= m.Width || y < 0 || y >= m.Height {
		return nil
	}
	arrayY := m.Height - 1 - y
	return &m.Blocks[arrayY][x]
}

func main() {
	fmt.Println("=== 测试新坐标系统（左下角为起始点，Y轴从下往上）===")
	
	testMap := NewTestMap()
	
	// 测试四个角的坐标
	fmt.Println("\n=== 测试四个角的坐标 ===")
	corners := []struct{
		name string
		x, y int
	}{
		{"左下角", 0, 0},
		{"右下角", 7, 0},
		{"左上角", 0, 7},
		{"右上角", 7, 7},
	}
	
	for _, corner := range corners {
		block := testMap.GetBlock(corner.x, corner.y)
		if block != nil {
			fmt.Printf("%s (%d,%d): 存储的坐标=(%d,%d), 地雷=%v, 周围地雷数=%d\n", 
				corner.name, corner.x, corner.y, block.X, block.Y, block.IsMine, block.NeighborMines)
		}
	}
	
	// 打印地雷分布（按新坐标系统显示）
	fmt.Println("\n=== 地雷分布（新坐标系统：Y轴从下往上）===")
	fmt.Println("Y轴")
	fmt.Println("↑")
	
	// 从上往下打印（Y=7到Y=0）
	for logicY := 7; logicY >= 0; logicY-- {
		fmt.Printf("%d ", logicY)
		for x := 0; x < 8; x++ {
			block := testMap.GetBlock(x, logicY)
			if block == nil {
				fmt.Print("? ")
				continue
			}
			if block.IsMine {
				fmt.Print("* ")
			} else {
				fmt.Printf("%d ", block.NeighborMines)
			}
		}
		fmt.Println()
	}
	fmt.Println("  0 1 2 3 4 5 6 7 → X轴")
	
	// 验证坐标一致性
	fmt.Println("\n=== 验证坐标一致性 ===")
	allConsistent := true
	for y := 0; y < 8; y++ {
		for x := 0; x < 8; x++ {
			block := testMap.GetBlock(x, y)
			if block == nil || block.X != x || block.Y != y {
				fmt.Printf("坐标不一致: 请求(%d,%d), 实际存储(%d,%d)\n", x, y, block.X, block.Y)
				allConsistent = false
			}
		}
	}
	
	if allConsistent {
		fmt.Println("✅ 所有坐标一致性检查通过")
	} else {
		fmt.Println("❌ 坐标一致性检查失败")
	}
	
	fmt.Println("\n=== 坐标系统验证完成 ===")
	fmt.Println("新坐标系统特点:")
	fmt.Println("- 左下角为(0,0)")
	fmt.Println("- 右下角为(7,0)")
	fmt.Println("- 左上角为(0,7)")
	fmt.Println("- 右上角为(7,7)")
	fmt.Println("- Y轴数值越大表示越靠上")
}
