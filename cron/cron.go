package cron

import (
	"minesweep/clientHandle"
	"minesweep/common/logx"
	comMsg "minesweep/common/msg"
	"minesweep/common/safe"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/model/common/base"
	"minesweep/model/dao"
	"minesweep/roommgr"
	"minesweep/usermgr"
	"minesweep/voiceroom"
	"sync"
	"time"
)

type Cron struct {
	stopCh chan int
	stopWg sync.WaitGroup
}

var cron = &Cron{
	stopCh: make(chan int),
}

func GetInstance() *Cron {
	return cron
}

func (c *Cron) Start() {
	c.stopWg.Add(1)
	safe.Go(func() {
		ticker := time.NewTicker(time.Second * 5)
		defer func() {
			logx.Info("Cron:Start 工作协程退出")
			ticker.Stop()
			c.stopWg.Done()
		}()

		var count int
		for {
			select {
			case <-c.stopCh:
				return
			case <-ticker.C:
				count++
				c.updateSrvStat()

				// 每分钟检查一次离线玩家
				if count%12 == 0 {
					c.checkOffline()
				}
			}
		}
	})
}

func (c *Cron) Stop() {
	close(c.stopCh)
	c.stopWg.Wait()
}

// updateSrvStat 更新游戏服务器统计
func (c *Cron) updateSrvStat() {
	var userCount, robotCount, viewerCount int
	if conf.Conf.Server.GameId == int(constvar.GameIDLive) {
		userCount, robotCount, viewerCount = voiceroom.GetInstance().GetPlayerCount()
	} else {
		userCount, robotCount, viewerCount = roommgr.GetInstance().GetPlayerCount()
	}
	srvStat := dao.GameSrvStat{
		SrvID:       conf.Conf.Server.ID,
		RoomCount:   roommgr.GetInstance().GetRoomCount(),
		PlayerCount: userCount,
		RobotCount:  robotCount,
		ViewerCount: viewerCount,
	}
	_ = dao.GroupDao.GameSrvStat.HSet(&srvStat)
}

// checkOffline 检查玩家是否离线
func (c *Cron) checkOffline() {
	users := usermgr.GetInstance().GetOfflineUsers()
	for _, user := range users {
		// 为了让消息变得有序，放入channel
		req := &base.GameReqMsg{
			AppChannel: user.AppChannel,
			AppID:      user.AppID,
			UserID:     user.UserID,
			Data:       comMsg.FromClientMsg{MsgID: constvar.MsgTypeUserOffline},
		}
		logx.Infof("checkOffline sendOfflineMsg userID:%v, lastMsgTime:%v", user.UserID, user.LastMsgTime)
		field := clientHandle.GetHandler().Field(req.AppChannel, req.AppID, req.UserID)
		ch, err := clientHandle.GetHandler().GetChan(field)
		if err != nil {
			logx.Errorf("checkOffline GetChan err:%v, field:%v", err, field)
			continue
		}
		ch <- req
	}
}
