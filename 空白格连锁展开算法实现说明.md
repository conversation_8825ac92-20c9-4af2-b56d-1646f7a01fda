# 空白格连锁展开算法实现说明

## 📋 功能概述

根据扫雷策划文档要求，实现了空白格连锁展开功能：
> 特殊安全区——空白格：若点击到空白格，将会把它以自身为中心的的9宫格展现出来。若还存在空白格，将继续展示，直至出现的空白格的9宫格内无空白格为止或扩散到已经点开的格子。

## 🔧 核心实现

### 1. 数据结构定义

```go
// RevealedBlock 连锁展开揭示的方块信息
type RevealedBlock struct {
    X             int    `json:"x"`             // x坐标
    Y             int    `json:"y"`             // y坐标
    NeighborMines int    `json:"neighborMines"` // 周围地雷数量
    IsMine        bool   `json:"isMine"`        // 是否是地雷
    TriggerUserID string `json:"triggerUserId"` // 触发揭示的玩家ID
}

// FloodFillResult 连锁展开结果
type FloodFillResult struct {
    RevealedBlocks []RevealedBlock `json:"revealedBlocks"` // 揭示的方块列表
    TotalRevealed  int             `json:"totalRevealed"`  // 总共揭示的方块数
    TriggerUserID  string          `json:"triggerUserId"`  // 触发连锁展开的玩家ID
    TriggerX       int             `json:"triggerX"`       // 触发点X坐标
    TriggerY       int             `json:"triggerY"`       // 触发点Y坐标
}
```

### 2. 核心算法：广度优先搜索(BFS)

```go
func (m *MineMap) ProcessFloodFill(startX, startY int, triggerUserID string) *FloodFillResult
```

**算法特点：**
- 使用广度优先搜索确保按层级展开
- 只有空白格（周围地雷数为0）才继续扩展
- 使用visited映射避免重复处理
- 8个方向的邻居检查（3×3邻域）

**关键逻辑：**
1. 验证起始点必须是空白格（NeighborMines == 0）
2. 使用队列进行BFS遍历
3. 对每个格子检查8个方向的邻居
4. 只有未揭示且非地雷的格子才会被揭示
5. 只有空白格才会将其邻居加入队列继续扩展

### 3. 游戏逻辑集成

在 `Room.updateMapWithPlayerActions()` 方法中集成：

```go
// 如果挖掘的是空白格，触发连锁展开
if !block.IsMine && block.NeighborMines == 0 {
    floodFillResult := slf.mineMap.ProcessFloodFill(action.X, action.Y, userID)
    if floodFillResult.TotalRevealed > 0 {
        slf.floodFillResults = append(slf.floodFillResults, floodFillResult)
    }
}
```

## 🎮 游戏流程集成

### 1. 回合管理
- 在 `switchToMinesweeping()` 中初始化 `floodFillResults`
- 在 `startNewRound()` 中清空上一回合的结果
- 在 `updateMapWithPlayerActions()` 中处理连锁展开

### 2. 数据广播
- 连锁展开结果包含在回合结束通知中
- 前端可以根据 `floodFillResults` 播放连锁展开动画
- 包含触发玩家信息，便于UI显示

### 3. 状态同步
- 连锁展开的方块会立即更新 `IsRevealed` 状态
- 自动更新 `RevealedCount` 计数
- 确保多人游戏状态一致性

## ✅ 测试验证

通过独立测试验证了算法的正确性：
- ✅ 正确识别和展开空白格区域
- ✅ 边界处理无越界问题
- ✅ 避免重复处理同一格子
- ✅ 正确处理有数字的边界格子
- ✅ 性能良好，8×8地图瞬间完成

## 🚀 使用示例

```go
// 玩家点击空白格后触发连锁展开
if !block.IsMine && block.NeighborMines == 0 {
    result := mineMap.ProcessFloodFill(x, y, userID)
    // result.RevealedBlocks 包含所有被连锁揭示的方块
    // 前端可以根据这些信息播放展开动画
}
```

## 📡 前端数据格式

回合结束通知中包含连锁展开数据：

```json
{
    "roundNumber": 1,
    "gameStatus": "round_end",
    "playerResults": [...],
    "mapData": {...},
    "floodFillResults": [
        {
            "revealedBlocks": [
                {
                    "x": 1,
                    "y": 0,
                    "neighborMines": 0,
                    "isMine": false,
                    "triggerUserId": "player_001"
                }
            ],
            "totalRevealed": 22,
            "triggerUserId": "player_001",
            "triggerX": 0,
            "triggerY": 0
        }
    ]
}
```

## 🎯 符合策划要求

- ✅ **9宫格展开**：检查8个方向的邻居（3×3邻域）
- ✅ **连锁展开**：空白格继续扩展，直到无空白格
- ✅ **边界处理**：正确处理已揭示格子的边界
- ✅ **性能优化**：使用BFS算法，时间复杂度O(n)
- ✅ **多人支持**：记录触发玩家，支持多人同时游戏

算法实现完全符合策划文档要求，为扫雷游戏提供了核心的连锁展开功能！
