package utils

import (
	"minesweep/common/msg"
	"minesweep/ecode"
	"minesweep/model/common/base"
	"minesweep/model/dao"
)

func Result(req *base.GameReqMsg, code int, data interface{}, message string) {
	clientMsg := &msg.ToClientMsg{
		MsgID: req.Data.MsgID,
		Code:  code,
		Data:  data,
		Msg:   message,
	}
	_ = dao.GroupDao.GameAckList.Add(req.FromID, &base.GameAckMsg{
		AppChannel: req.AppChannel,
		AppID:      req.AppID,
		UserID:     req.UserID,
		PlatRoomID: req.PlatRoomID,
		Data:       clientMsg,
	})
}

func Ok(req *base.GameReqMsg) {
	Result(req, ecode.OK, map[string]interface{}{}, "success")
}

func OkWithDetailed(req *base.GameReqMsg, data interface{}) {
	Result(req, ecode.OK, data, "success")
}

func FailWithDetailed(req *base.GameReqMsg, data interface{}, code int) {
	Result(req, code, data, ecode.GetMsg(code))
}

func FailWithDetailedV2(req *base.GameReqMsg, data interface{}, code int, message string) {
	Result(req, code, data, message)
}

func Fail(req *base.GameReqMsg, code int) {
	Result(req, code, map[string]interface{}{}, ecode.GetMsg(code))
}

func FailV2(req *base.GameReqMsg, code int, message string) {
	Result(req, code, map[string]interface{}{}, message)
}
