package roommgr

import (
	"minesweep/constvar"
)

func (slf *Room) BuildMap() {
	switch slf.MapType {
	case constvar.MapTypeHexagon:
		slf.BuildHexagonMap() // 新增六边形地图构建方法
	default:
		slf.BuildGridMap() // 重命名原来的 BuildFiftyMap
	}
}

// BuildGridMap  生成类型1方块的地图
func (slf *Room) BuildGridMap() {
	slf.blockMap = NewBlockMap(slf.RoomID, 0)

}

// BuildHexagonMap 生成类型2六边形的地图
func (slf *Room) BuildHexagonMap() {
	slf.blockMap = NewBlockMap(slf.RoomID, 1)

}
