package roommgr

import (
	"minesweep/constvar"
)

// getUserEffectProps 获取用户有效的的道具列表
func (slf *Room) getUserEffectProps(userID string) []constvar.GameProp {
	var propMap = make(map[constvar.GameProp]bool)
	for i := 0; i < slf.playerNum; i++ {
		for _, v := range slf.allPlayerInfo[i].UsingProps {
			switch v.PropType {
			case constvar.GamePropReverse:
				// 后退，对其他人有效
				if slf.allPlayerInfo[i].UserID == userID {
					continue
				}
				propMap[v.PropType] = true
			case constvar.GamePropShield:
				// 盾牌，对自己有效
				if slf.allPlayerInfo[i].UserID != userID {
					continue
				}
				propMap[v.PropType] = true
			case constvar.GamePropBlizzard:
				// 冰冻，对所有人有效
				propMap[v.PropType] = true
			case constvar.GamePropMultiplier:
				// 加倍，对自己有效
				if slf.allPlayerInfo[i].UserID != userID {
					continue
				}
				propMap[v.PropType] = true
			case constvar.GamePropAdvancement:
				// 前进，对自己有效
				if slf.allPlayerInfo[i].UserID != userID {
					continue
				}
				propMap[v.PropType] = true
			}
		}
	}
	var propList = make([]constvar.GameProp, 0)
	for propType, _ := range propMap {
		propList = append(propList, propType)
	}
	return propList
}
