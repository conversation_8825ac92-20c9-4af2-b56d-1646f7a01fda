package roommgr

import (
	"fmt"
	"math/rand"
	"minesweep/common/logx"
	"time"
)

// 扫雷游戏常量
const (
	MineMapWidth  = 8  // 地图宽度
	MineMapHeight = 8  // 地图高度
	MineCount     = 13 // 地雷数量

)

// MineBlock 扫雷方块结构（mapType=0专用）
type MineBlock struct {
	X             int      `json:"x"`             // x坐标(0-7)
	Y             int      `json:"y"`             // y坐标(0-7)
	IsMine        bool     `json:"isMine"`        // 是否是地雷
	IsRevealed    bool     `json:"isRevealed"`    // 是否已揭开
	IsMarked      bool     `json:"isMarked"`      // 是否被标记为地雷
	NeighborMines int      `json:"neighborMines"` // 周围地雷数量(0-8)
	Players       []string `json:"players"`       // 当前格子中的玩家ID列表
}

// MineMap 扫雷地图
type MineMap struct {
	Width         int           `json:"width"`         // 地图宽度(8)
	Height        int           `json:"height"`        // 地图高度(8)
	MineCount     int           `json:"mineCount"`     // 地雷总数(13)
	Blocks        [][]MineBlock `json:"blocks"`        // 地图块二维数组[y][x]
	RevealedCount int           `json:"revealedCount"` // 已揭开的非地雷格子数
	RoomID        int64         `json:"roomId"`        // 房间ID（用于日志）
}

// RoundAction 回合操作
type RoundAction struct {
	UserID    string `json:"userId"`    // 玩家ID
	X         int    `json:"x"`         // x坐标
	Y         int    `json:"y"`         // y坐标
	Action    int    `json:"action"`    // 操作类型(1-挖掘，2-标记地雷)
	Timestamp int64  `json:"timestamp"` // 操作时间戳
	Score     int    `json:"score"`     // 本次操作得分（回合结束时计算）
}

// RevealedBlock 连锁展开揭示的方块信息
type RevealedBlock struct {
	X             int    `json:"x"`             // x坐标
	Y             int    `json:"y"`             // y坐标
	NeighborMines int    `json:"neighborMines"` // 周围地雷数量
	IsMine        bool   `json:"isMine"`        // 是否是地雷
	TriggerUserID string `json:"triggerUserId"` // 触发揭示的玩家ID
}

// FloodFillResult 连锁展开结果
type FloodFillResult struct {
	RevealedBlocks []RevealedBlock `json:"revealedBlocks"` // 揭示的方块列表
	TotalRevealed  int             `json:"totalRevealed"`  // 总共揭示的方块数
	TriggerUserID  string          `json:"triggerUserId"`  // 触发连锁展开的玩家ID
	TriggerX       int             `json:"triggerX"`       // 触发点X坐标
	TriggerY       int             `json:"triggerY"`       // 触发点Y坐标
}

// PlayerActionDisplay 玩家操作展示（第20秒展示阶段使用）
type PlayerActionDisplay struct {
	UserID string `json:"userId"` // 玩家ID
	X      int    `json:"x"`      // 操作坐标x
	Y      int    `json:"y"`      // 操作坐标y
	Action int    `json:"action"` // 操作类型(1-挖掘，2-标记)
}

// PlayerRoundResult 玩家回合结果（第25秒回合结束时使用）
type PlayerRoundResult struct {
	UserID        string `json:"userId"`        // 玩家ID
	X             int    `json:"x"`             // 操作坐标x
	Y             int    `json:"y"`             // 操作坐标y
	Action        int    `json:"action"`        // 操作类型(1-挖掘，2-标记)
	Score         int    `json:"score"`         // 本回合得分
	IsFirstChoice bool   `json:"isFirstChoice"` // 是否为首选玩家
}

// PlayerFinalResult 玩家最终结果
type PlayerFinalResult struct {
	UserID     string `json:"userId"`     // 玩家ID
	TotalScore int    `json:"totalScore"` // 总得分
	Rank       int    `json:"rank"`       // 最终排名
}

// PlayerFirstChoiceBonus 首选玩家奖励推送
type PlayerFirstChoiceBonus struct {
	UserID      string `json:"userId"`      // 玩家ID
	RoundNumber int    `json:"roundNumber"` // 回合编号
	BonusScore  int    `json:"bonusScore"`  // 首选玩家奖励分数（固定+1）
	TotalScore  int    `json:"totalScore"`  // 累计总得分（包含此奖励）
}

// NewMineMap 创建新的扫雷地图
func NewMineMap(roomID int64) *MineMap {
	const (
		mapWidth  = 8  // 固定8×8地图
		mapHeight = 8  // 固定8×8地图
		mineCount = 13 // 固定13个地雷
	)

	mineMap := &MineMap{
		Width:         mapWidth,
		Height:        mapHeight,
		MineCount:     mineCount,
		Blocks:        make([][]MineBlock, mapHeight),
		RevealedCount: 0,
		RoomID:        roomID,
	}

	// 初始化地图块
	for y := 0; y < mapHeight; y++ {
		mineMap.Blocks[y] = make([]MineBlock, mapWidth)
		for x := 0; x < mapWidth; x++ {
			mineMap.Blocks[y][x] = MineBlock{
				X:             x,
				Y:             y,
				IsMine:        false,
				IsRevealed:    false,
				IsMarked:      false,
				NeighborMines: 0,
				Players:       make([]string, 0),
			}
		}
	}

	// 生成地雷分布
	mineMap.generateMines()

	// 计算每个格子周围的地雷数量
	mineMap.calculateNeighborMines()

	logx.Infof("扫雷地图创建成功 RoomID:%v Size:%dx%d MineCount:%d", roomID, mapWidth, mapHeight, mineCount)
	return mineMap
}

// generateMines 生成地雷分布
func (m *MineMap) generateMines() {
	// 使用当前时间作为随机种子，确保每次生成的地图都不同
	rand.Seed(time.Now().UnixNano())

	// 生成所有可能的位置
	totalPositions := m.Width * m.Height
	positions := make([]int, totalPositions)
	for i := 0; i < totalPositions; i++ {
		positions[i] = i
	}

	// 随机打乱位置数组
	rand.Shuffle(totalPositions, func(i, j int) {
		positions[i], positions[j] = positions[j], positions[i]
	})

	// 取前MineCount个位置作为地雷位置
	minePositions := positions[:m.MineCount]

	// 在选定位置放置地雷
	for _, pos := range minePositions {
		x := pos % m.Width
		y := pos / m.Width
		m.Blocks[y][x].IsMine = true
	}

	logx.Infof("地雷分布生成完成 RoomID:%v MinePositions:%v", m.RoomID, minePositions)
}

// calculateNeighborMines 计算每个格子周围的地雷数量
func (m *MineMap) calculateNeighborMines() {
	// 8个方向的偏移量（3×3邻域，不包括中心点）
	directions := [][]int{
		{-1, -1}, {-1, 0}, {-1, 1}, // 上方三个
		{0, -1}, {0, 1}, // 左右两个
		{1, -1}, {1, 0}, {1, 1}, // 下方三个
	}

	for y := 0; y < m.Height; y++ {
		for x := 0; x < m.Width; x++ {
			if m.Blocks[y][x].IsMine {
				continue // 地雷格子不需要计算周围地雷数
			}

			mineCount := 0
			// 检查8个方向的邻居
			for _, dir := range directions {
				nx, ny := x+dir[1], y+dir[0]
				// 检查边界
				if nx >= 0 && nx < m.Width && ny >= 0 && ny < m.Height {
					if m.Blocks[ny][nx].IsMine {
						mineCount++
					}
				}
			}
			m.Blocks[y][x].NeighborMines = mineCount
		}
	}

	logx.Infof("周围地雷数量计算完成 RoomID:%v", m.RoomID)
}

// GetBlock 获取指定坐标的方块
func (m *MineMap) GetBlock(x, y int) *MineBlock {
	if x < 0 || x >= m.Width || y < 0 || y >= m.Height {
		return nil
	}
	return &m.Blocks[y][x]
}

// IsValidPosition 检查坐标是否有效
func (m *MineMap) IsValidPosition(x, y int) bool {
	return x >= 0 && x < m.Width && y >= 0 && y < m.Height
}

// GetMinePositions 获取所有地雷位置（用于调试）
func (m *MineMap) GetMinePositions() [][]int {
	var minePositions [][]int
	for y := 0; y < m.Height; y++ {
		for x := 0; x < m.Width; x++ {
			if m.Blocks[y][x].IsMine {
				minePositions = append(minePositions, []int{x, y})
			}
		}
	}
	return minePositions
}

// GetRevealedNonMineCount 获取已揭开的非地雷格子数量
func (m *MineMap) GetRevealedNonMineCount() int {
	count := 0
	for y := 0; y < m.Height; y++ {
		for x := 0; x < m.Width; x++ {
			if m.Blocks[y][x].IsRevealed && !m.Blocks[y][x].IsMine {
				count++
			}
		}
	}
	return count
}

// IsGameComplete 检查游戏是否完成（所有非地雷格子都被揭开）
func (m *MineMap) IsGameComplete() bool {
	totalNonMineBlocks := m.Width*m.Height - m.MineCount
	return m.GetRevealedNonMineCount() >= totalNonMineBlocks
}

// CalculateActionScore 计算单个操作的基础得分（不包含首选奖励）
func (m *MineMap) CalculateActionScore(action *RoundAction) int {
	// 验证坐标有效性
	if !m.IsValidPosition(action.X, action.Y) {
		logx.Errorf("计算得分时坐标无效 RoomID:%v x:%v y:%v", m.RoomID, action.X, action.Y)
		return 0
	}

	block := &m.Blocks[action.Y][action.X]

	if action.Action == 1 { // 挖掘操作
		if block.IsMine {
			return -12 // 挖到地雷扣12分
		} else {
			return 6 // 挖到安全区得6分
		}
	} else if action.Action == 2 { // 标记操作
		if block.IsMine {
			return 10 // 正确标记地雷得10分
		} else {
			return 0 // 错误标记得0分
		}
	}

	logx.Errorf("未知操作类型 RoomID:%v UserID:%v Action:%v", m.RoomID, action.UserID, action.Action)
	return 0
}

// ProcessFloodFill 处理空白格连锁展开（广度优先搜索）
// 注意：此方法假设起始点已经被揭示，只处理连锁展开的邻居格子
func (m *MineMap) ProcessFloodFill(startX, startY int, triggerUserID string) *FloodFillResult {
	// 验证起始坐标
	if !m.IsValidPosition(startX, startY) {
		logx.Errorf("连锁展开起始坐标无效 RoomID:%v x:%v y:%v", m.RoomID, startX, startY)
		return &FloodFillResult{
			RevealedBlocks: []RevealedBlock{},
			TotalRevealed:  0,
			TriggerUserID:  triggerUserID,
			TriggerX:       startX,
			TriggerY:       startY,
		}
	}

	startBlock := &m.Blocks[startY][startX]

	// 只有空白格（周围地雷数为0）才能触发连锁展开
	if startBlock.IsMine || startBlock.NeighborMines != 0 {
		return &FloodFillResult{
			RevealedBlocks: []RevealedBlock{},
			TotalRevealed:  0,
			TriggerUserID:  triggerUserID,
			TriggerX:       startX,
			TriggerY:       startY,
		}
	}

	// 8个方向的偏移量（3×3邻域，不包括中心点）
	directions := [][]int{
		{-1, -1}, {-1, 0}, {-1, 1}, // 上方三个
		{0, -1}, {0, 1},             // 左右两个
		{1, -1}, {1, 0}, {1, 1},     // 下方三个
	}

	var revealedBlocks []RevealedBlock
	visited := make(map[string]bool) // 使用字符串键 "x,y" 来标记已访问的格子
	queue := [][]int{{startX, startY}} // BFS队列

	// 标记起始点为已访问（但不重复揭示）
	startKey := fmt.Sprintf("%d,%d", startX, startY)
	visited[startKey] = true

	for len(queue) > 0 {
		// 取出队列头部
		current := queue[0]
		queue = queue[1:]

		x, y := current[0], current[1]

		// 将8个方向的邻居加入处理队列
		for _, dir := range directions {
			nx, ny := x+dir[0], y+dir[1]
			neighborKey := fmt.Sprintf("%d,%d", nx, ny)

			// 跳过已访问的格子和无效坐标
			if visited[neighborKey] || !m.IsValidPosition(nx, ny) {
				continue
			}

			visited[neighborKey] = true
			neighborBlock := &m.Blocks[ny][nx]

			// 跳过地雷和已揭示的格子
			if neighborBlock.IsMine || neighborBlock.IsRevealed {
				continue
			}

			// 揭示当前邻居格子
			neighborBlock.IsRevealed = true
			m.RevealedCount++

			// 记录揭示的方块信息
			revealedBlock := RevealedBlock{
				X:             nx,
				Y:             ny,
				NeighborMines: neighborBlock.NeighborMines,
				IsMine:        neighborBlock.IsMine,
				TriggerUserID: triggerUserID,
			}
			revealedBlocks = append(revealedBlocks, revealedBlock)

			// 如果这个邻居也是空白格，将其加入队列继续扩展
			if neighborBlock.NeighborMines == 0 {
				queue = append(queue, []int{nx, ny})
			}
		}
	}

	result := &FloodFillResult{
		RevealedBlocks: revealedBlocks,
		TotalRevealed:  len(revealedBlocks),
		TriggerUserID:  triggerUserID,
		TriggerX:       startX,
		TriggerY:       startY,
	}

	logx.Infof("连锁展开完成 RoomID:%v TriggerUser:%v StartPos:(%d,%d) RevealedCount:%d",
		m.RoomID, triggerUserID, startX, startY, len(revealedBlocks))

	return result
}
