package roommgr

import (
	"fmt"
	"math/rand"
	"minesweep/common/iface"
	"minesweep/common/logx"
	"minesweep/common/safe"
	"minesweep/common/timertask"
	"minesweep/common/tools"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/localmgr"
	"minesweep/model/common/base"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
	"minesweep/model/dao"
	"minesweep/usermgr"
	"sort"
	"sync"
	"sync/atomic"
	"time"
)

type Room struct {
	sync.RWMutex                      // allUser的操作加锁
	RoomID          int64             // 房间ID
	GameCount       int               // 第几局
	RoomType        constvar.RoomType // 房间类型
	CreateTime      time.Time         // 房间创建时间
	LastMsgTime     int64             // 最近消息时间戳
	apiScene        constvar.ApiScene // api调用场景
	apiCreateExtend string            // api创建扩展参数

	// 游戏数据
	gameStatus    constvar.GameStatus        // 游戏状态
	countDown     int                        // 游戏状态下玩家Token倒计时(从N倒计时到0)
	allPlayerInfo []*PlayerInfo              // 座位上玩家的游戏数据
	allUser       map[string]*RoomUser       // 房间内的所有玩家
	curTokenPos   int                        // 当前Token座位号
	timerTask     timertask.TimerTask        // 延时任务
	firstMoves    map[string]*base.FirstMove // 用户先手列表
	blockMap      *BlockMap                  // 蛇梯棋游戏地图
	mineMap       *MineMap                   // 扫雷游戏地图

	// 扫雷游戏回合操作管理
	currentRound       int                     // 当前回合编号
	roundActions       map[string]*RoundAction // 当前回合玩家操作（userID -> action）
	roundStartTime     int64                   // 回合开始时间戳
	firstPlayerInRound string                  // 当前回合首选玩家ID（享受+1分奖励）

	// 扫雷游戏得分管理
	playerTotalScores map[string]int // 玩家总分累计（userID -> totalScore）

	rollTimes       int              // 掷骰子次数
	maxStopTimes    int              // 最大停止前进次数
	ticker          *time.Ticker     // 状态定时器
	taskTicker      *time.Ticker     // 任务定时器
	isSettled       bool             // 是否已结算
	isFirstMoveEnd  bool             // 客户端先手动画是否结束
	playerPointPool PointPool        // 玩家点数池子
	easyPointPool   PointPool        // 容易机器人点数池子
	hardPointPool   PointPool        // 困难机器人点数池子
	unSafeRand      *rand.Rand       // 线程不安全随机数，效率高，无锁
	voiceRoom       iface.IVoiceRoom // 语聊房
	allConnSrv      sync.Map         // 所有的连接服务器ID
	settleType      base.SettleType  // 结算类型

	// 退出
	stopCh        chan int                  // 退出
	stopWg        sync.WaitGroup            // 退出
	isClosed      bool                      // 房间是否已关闭
	isStopped     bool                      // 房间是否已停止
	refuseService int32                     // 拒绝服务
	stopMu        sync.Mutex                // 退出锁
	msgChan       chan *request.PackMessage // 消息队列

	// 房间配置
	appChannel string // 平台渠道
	appID      int64  // appID
	//propMode   int              // 道具模式
	playerNum  int              // 玩家人数
	MapType    constvar.MapType // 地图类型
	fee        int              // 房间费
	coinType   int              // 支持的货币类型
	roomConfig conf.RoomConf    // 场次级别配置
	channelCfg conf.ChannelConf // 平台相关的配置
}

// NewRoom 创建新房间
func NewRoom(appChannel string, appID int64, playerNum int, fee int, roomType constvar.RoomType) *Room {
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(appChannel, appID)
	roomConf := channelCfg.GetRoomConf(roomType)
	if roomConf == nil {
		return nil
	}
	if channelCfg.EasyRobot.PointWeights.Empty() ||
		channelCfg.HardRobot.PointWeights.Empty() ||
		channelCfg.GamePlayer.PointWeights.Empty() {
		logx.Errorf("NewRoom EasyWeights or HardWeights or PlayerWeights config err")
		return nil
	}
	// 随机生成地图类型，确保返回 constvar.MapType 类型
	mapType := generateRandomMapType() // 这个函数返回 constvar.MapType
	var roomID = getNewRoomID()
	r := &Room{
		RoomID:       roomID,
		GameCount:    1,
		RoomType:     roomType,
		MapType:      mapType,
		CreateTime:   time.Now(),
		allUser:      make(map[string]*RoomUser),
		maxStopTimes: tools.Rand(9, 12),
		curTokenPos:  0,
		firstMoves:   map[string]*base.FirstMove{},
		blockMap:     NewBlockMap(roomID, mapType),
		stopCh:       make(chan int),
		msgChan:      make(chan *request.PackMessage, 1000),
		appChannel:   appChannel,
		appID:        appID,
		//propMode:     propMode,
		playerNum:   playerNum,
		fee:         fee,
		coinType:    channelCfg.CoinType,
		roomConfig:  *roomConf,
		channelCfg:  *channelCfg,
		LastMsgTime: time.Now().Unix(),
		unSafeRand:  rand.New(rand.NewSource(time.Now().UnixNano())), // 线程不安全
	}
	for i := 0; i < playerNum; i++ {
		r.allPlayerInfo = append(r.allPlayerInfo, NewPlayerInfo())
	}
	r.BuildMap()
	//r.blockMap.print()

	logx.Infof("房间创建成功 appChannel:%v appID:%v playerNum:%v mapType:%v fee:%v roomID:%v", appChannel, appID, playerNum, mapType, fee, r.RoomID)
	return r
}

// 确保这个函数返回正确的类型
func generateRandomMapType() constvar.MapType {
	if tools.Rand(0, 1) == 0 {
		return constvar.MapTypeGrid // constvar.MapType(0)
	}
	return constvar.MapTypeHexagon // constvar.MapType(1)
}

// getNewRoomID 获取一个新的房间id
func getNewRoomID() int64 {
	var lockID = "newRoomID"
	dao.GroupDao.GlobalLock.Lock(lockID, time.Second*2)
	defer dao.GroupDao.GlobalLock.Unlock(lockID)

	time.Sleep(time.Millisecond * 2)
	return time.Now().UnixMilli()
}

// Start 启动房间逻辑
func (slf *Room) Start() {
	slf.ticker = time.NewTicker(time.Second)
	slf.taskTicker = time.NewTicker(time.Millisecond * 50)

	// 根据地图类型决定初始化方式
	switch slf.MapType {
	case constvar.MapTypeGrid:
		// 方形地图：扫雷游戏，直接设置为扫雷状态
		slf.gameStatus = constvar.GameStatusMinesweeping
		slf.countDown = 0
	case constvar.MapTypeHexagon:
		// 六边形地图：蛇梯棋游戏，使用原有初始化
		slf.InitPlayerPointPool()
		slf.InitEasyPointPool()
		slf.InitHardPointPool()
		slf.SwitchToFirstMove()
		slf.SetRobotLevel()
	}

	// 语聊房上报游戏开始
	if slf.voiceRoom != nil {
		slf.ReportGameStart()
	}

	slf.stopWg.Add(1)
	safe.Go(func() {
		defer func() {
			slf.ticker.Stop()
			slf.taskTicker.Stop()
			slf.stopWg.Done()
			slf.isStopped = true
			logx.Infof("RoomID:%v ticker defer end", slf.RoomID)
		}()

		for {
			select {
			case <-slf.stopCh:
				return
			case <-slf.ticker.C:
				slf.OnTimer()
			default:
				// 低优先级任务执行前，先检查是否有高优任务
				select {
				case <-slf.stopCh:
					return
				case <-slf.taskTicker.C:
					slf.timerTask.ExecTask()
				case msg := <-slf.msgChan:
					// OnTimer panic 崩了之后，msgChan消息处理不了，用户EnterRoom消息无法处理，会卡死前端界面
					slf.ProcessMessage(msg)
				}
			}
		}
	})

	// 通知所有人开始游戏
	var users []*response.RoomUser
	for _, v := range slf.allUser {
		if v.Pos < 0 {
			continue
		}

		roomUser := &response.RoomUser{
			UserID:   v.UserID,
			NickName: v.NickName,
			Avatar:   v.Avatar,
			Pos:      v.Pos,
			Coin:     v.Coin,
			Status:   v.UserStatus,
		}

		// 根据地图类型填充不同的游戏相关字段
		switch slf.MapType {
		case constvar.MapTypeGrid:
			// 方形地图：填充蛇梯棋相关字段（保持向后兼容）
			roomUser.SkinChessID = v.SkinChessID
		case constvar.MapTypeHexagon:
			// 六边形地图：填充扫雷游戏相关字段
			roomUser.MinesFound = 0
			roomUser.BlocksRevealed = 0
			roomUser.GameScore = 0
			roomUser.IsAlive = true
		}

		users = append(users, roomUser)
	}
	sort.Slice(users, func(i, j int) bool {
		return users[i].Pos < users[j].Pos
	})

	// 根据地图类型决定返回的数据结构
	var validHexCoords []*response.HexCoord
	var mapConfig *response.MapConfig

	switch slf.MapType {
	case constvar.MapTypeGrid:
		// 方形地图：返回地图配置信息（扫雷游戏）
		// 需要先初始化扫雷地图以获取配置信息
		if slf.mineMap == nil {
			slf.mineMap = NewMineMap(slf.RoomID)
		}
		mapConfig = &response.MapConfig{
			Width:     slf.mineMap.Width,
			Height:    slf.mineMap.Height,
			MineCount: slf.mineMap.MineCount,
		}
	case constvar.MapTypeHexagon:
		// 六边形地图：返回ValidHexCoords（蛇梯棋游戏）
		validHexCoords = slf.getPresetHexCoords()
	}

	slf.Broadcast(constvar.MsgTypeGameStart, ecode.OK, &response.NoticeStartGame{
		RoomID:         slf.RoomID,
		RoomType:       slf.RoomType,
		PlayerNum:      slf.playerNum,
		MapType:        slf.MapType,
		Fee:            slf.fee,
		Users:          users,
		GameStatus:     slf.gameStatus,
		CountDown:      slf.countDown,
		ValidHexCoords: validHexCoords,
		MapConfig:      mapConfig,
	})
	logx.Infof("游戏开始通知发送成功 RoomID:%v MapType:%v PlayerNum:%v", slf.RoomID, slf.MapType, slf.playerNum)

	// 根据地图类型决定后续流程
	switch slf.MapType {
	case constvar.MapTypeGrid:
		// 方形地图：扫雷游戏流程，等待2秒后进入扫雷状态
		slf.scheduleStartMinesweeper()
	case constvar.MapTypeHexagon:
		// 六边形地图：蛇梯棋游戏，保持现有逻辑
		// TODO: 后续实现六边形地图的扫雷游戏
	}
}

// getPresetHexCoords 获取预设的六边形坐标数据（占位符）
func (slf *Room) getPresetHexCoords() []*response.HexCoord {
	// 基于文档示例的预设六边形地图坐标
	// 这是一个临时的占位符数据，后续六边形地图功能完成后会替换为动态生成
	return []*response.HexCoord{
		{Q: 0, R: 0},
		{Q: 1, R: 0},
		{Q: 1, R: -1},
		{Q: 0, R: -1},
		{Q: -1, R: 0},
		{Q: -1, R: 1},
		{Q: 0, R: 1},
		{Q: 2, R: 0},
		{Q: 2, R: -1},
		{Q: 2, R: -2},
		{Q: 1, R: -2},
		{Q: 0, R: -2},
		{Q: -1, R: -1},
		{Q: -2, R: 0},
		{Q: -2, R: 1},
		{Q: -2, R: 2},
		{Q: -1, R: 2},
		{Q: 0, R: 2},
		{Q: 1, R: 1},
	}
}

// Stop 停止房间逻辑
func (slf *Room) Stop() {
	logx.Infof("RoomID:%v Stop begin, isClosed:%v", slf.RoomID, slf.isClosed)
	slf.stopMu.Lock()
	if !slf.isClosed {
		close(slf.stopCh)
		slf.isClosed = true
		slf.stopWg.Wait()
	}
	slf.stopMu.Unlock()
}

func (slf *Room) OnMsg(msg *request.PackMessage) {
	select {
	case slf.msgChan <- msg:
		// 写成功
	default:
		// 写失败（channel 满了）
		logx.Errorf("RoomID:%v msgChan full, dropping msg:%v", slf.RoomID, tools.ToString(msg))
	}
}

// GetAllUser 获取所有玩家(包括旁观者)
func (slf *Room) GetAllUser() map[string]*RoomUser {
	slf.RLock()
	defer slf.RUnlock()

	var allUser = make(map[string]*RoomUser)
	for _, v := range slf.allUser {
		allUser[v.UserID] = v
	}
	return allUser
}

// GetSitUser 获取坐在座位上的某玩家
func (slf *Room) GetSitUser(userID string) *RoomUser {
	slf.RLock()
	defer slf.RUnlock()

	user, ok := slf.allUser[userID]
	if !ok {
		return nil
	}
	if user.Pos < 0 {
		return nil
	}
	return user
}

// GetUser 获取某玩家
func (slf *Room) GetUser(userID string) *RoomUser {
	slf.RLock()
	defer slf.RUnlock()

	user, ok := slf.allUser[userID]
	if !ok {
		return nil
	}
	return user
}

// RemoveUser 移除某玩家，并踢出房间
func (slf *Room) RemoveUser(userID string) {
	slf.Lock()
	delete(slf.allUser, userID)
	slf.Unlock()

	for _, v := range slf.allPlayerInfo {
		if v.UserID == userID {
			v.ResetAll()
			break
		}
	}

	localmgr.GetInstance().RmvLocalByRoomID(slf.appChannel, slf.appID, userID, slf.RoomID)
	//slf.Broadcast(constvar.MsgTypeKickOutUser, ecode.OK, &response.NoticeUserKickOut{UserID: userID})
	logx.Infof("用户移除房间成功 RoomID:%v userID:%v", slf.RoomID, userID)
}

// GetPlayerCount 获取玩家的数量
func (slf *Room) GetPlayerCount() (int, int, int) {
	slf.RLock()
	defer slf.RUnlock()

	var (
		playerCount int // 玩家数量
		robotCount  int // 机器人数量
		viewerCount int // 旁观者玩家数量
	)
	for _, v := range slf.allUser {
		if v.IsRobot() {
			robotCount++
			continue
		}

		if v.Pos >= 0 {
			playerCount++
		} else {
			viewerCount++
		}
	}
	return playerCount, robotCount, viewerCount
}

// GetRoundID 获取当前第几轮
func (slf *Room) GetRoundID() string {
	return fmt.Sprintf("%v_%v", slf.RoomID, slf.GameCount)
}

// IsPlaying 是否在游戏中
func (slf *Room) IsPlaying() bool {
	if slf.gameStatus <= 0 {
		return false
	}
	return true
}

// UserJoin 玩家进入房间
func (slf *Room) UserJoin(user *RoomUser) bool {
	slf.Lock()
	defer slf.Unlock()
	defer slf.UpdateAllConnSrv(user.UserID)

	if _, ok := slf.allUser[user.UserID]; ok {
		logx.Infof("用户重复加入房间成功 RoomID:%v userID:%v", slf.RoomID, user.UserID)
		return true
	}

	// 验证棋子的皮肤Id
	if user.SkinChessID.Type() == 0 {
		user.SkinChessID = constvar.ProductIDDefaultChess
	}

	u := &RoomUser{
		NickName:     user.NickName,
		Avatar:       user.Avatar,
		UserID:       user.UserID,
		Coin:         user.Coin,
		SSToken:      user.SSToken,
		ClientIp:     user.ClientIp,
		PlatRoomID:   user.PlatRoomID,
		IdentityType: user.IdentityType,
		UserStatus:   constvar.UserStatusSit,
		LastMsgTime:  time.Now().Unix(),
		Pos:          user.Pos,
		SkinChessID:  user.SkinChessID,
		IsOffline:    user.IsOffline, // api创建的语聊房，默认是离线的
	}
	slf.allUser[user.UserID] = u
	logx.Infof("用户加入房间成功 RoomID:%v userID:%v_%v appChannel:%v_%v", slf.RoomID, user.UserID, user.IdentityType, slf.appChannel, slf.appID)
	return true
}

// ViewerJoin 旁观者进入房间
func (slf *Room) ViewerJoin(user *RoomUser) {
	slf.Lock()
	defer slf.Unlock()
	defer slf.UpdateAllConnSrv(user.UserID)

	if _, ok := slf.allUser[user.UserID]; ok {
		logx.Infof("旁观者重复加入房间成功 RoomID:%v userID:%v", slf.RoomID, user.UserID)
		return
	}

	// 验证棋子的皮肤Id
	if user.SkinChessID.Type() == 0 {
		user.SkinChessID = constvar.ProductIDDefaultChess
	}

	u := &RoomUser{
		NickName:     user.NickName,
		Avatar:       user.Avatar,
		UserID:       user.UserID,
		Coin:         user.Coin,
		PlatRoomID:   user.PlatRoomID,
		IdentityType: user.IdentityType,
		UserStatus:   constvar.UserStatusStand,
		Pos:          -1,
		SkinChessID:  user.SkinChessID,
	}
	slf.allUser[user.UserID] = u
	logx.Infof("旁观者加入房间成功 RoomID:%v userID:%v", slf.RoomID, user.UserID)
}

// SetRefuseService 设置拒绝服务
func (slf *Room) SetRefuseService() {
	atomic.AddInt32(&slf.refuseService, 1)
}

// IsRefuseService 是否拒绝服务
func (slf *Room) IsRefuseService() bool {
	return atomic.AddInt32(&slf.refuseService, 0) > 0
}

// CheckRoom 检查房间是否运行正常
func (slf *Room) CheckRoom() bool {
	// 房间已停止，或者某个游戏状态卡死超过5分钟，直接解散房间
	if slf.isStopped || slf.countDown < -300 {
		logx.Errorf("CheckRoom remove RoomID:%v stopped", slf.RoomID)
		slf.ForceCloseRoom()
		return false
	}

	var userCount int
	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		if v.IsRobot() {
			continue
		}
		userCount++
	}

	// 房间无人，或者超过60分钟无客户端消息，回收房间
	if time.Now().Unix()-slf.LastMsgTime > 3600 {
		logx.Errorf("CheckRoom remove RoomID:%v lastMsg timeout, userCount:%v", slf.RoomID, userCount)
		slf.ForceCloseRoom()
		return false
	}
	return true
}

// SetVoiceRoom 设置对应的语聊房
func (slf *Room) SetVoiceRoom(voiceRoom iface.IVoiceRoom, apiScene constvar.ApiScene, extend string) {
	slf.voiceRoom = voiceRoom
	slf.apiScene = apiScene
	slf.apiCreateExtend = extend
}

// TestBlockMap 测试游戏地图
func (slf *Room) TestBlockMap() {
	slf.BuildMap()
	if err := slf.blockMap.check(); err != nil {
		logx.Infof("TestBlockMap check err:%v", err)
		slf.blockMap.print()
	}
}

// PrintRoom 打印该房间
func (slf *Room) PrintRoom() {
	slf.RLock()
	defer slf.RUnlock()

	logx.Infof("===PrintRoom RoomID:%v gameStatus:%v countDown:%v curTokenPos:%v isSettled:%v, maxStopTimes:%v", slf.RoomID, slf.gameStatus, slf.countDown, slf.curTokenPos, slf.isSettled, slf.maxStopTimes)
	logx.Infof("===PrintRoom RoomID:%v allPlayerInfo:%+v", slf.RoomID, tools.GetObj(slf.allPlayerInfo))
	logx.Infof("===PrintRoom RoomID:%v allUser:%+v", slf.RoomID, tools.GetObj(slf.allUser))
}

// UpdateAllConnSrv 更新所有的连接服务器
func (slf *Room) UpdateAllConnSrv(userID string) {
	user := usermgr.GetInstance().GetUserById(slf.appChannel, slf.appID, userID)
	if user != nil {
		slf.allConnSrv.Store(user.ConnSrvID, time.Now().UnixNano())
	}

	var connList []string
	slf.allConnSrv.Range(func(key, value any) bool {
		connSrvID := key.(string)
		connList = append(connList, connSrvID)
		return true
	})
	logx.Infof("RoomID:%v UpdateAllConnSrv connList:%+v", slf.RoomID, connList)
}

// scheduleStartMinesweeper 安排启动扫雷游戏（2秒延迟）
func (slf *Room) scheduleStartMinesweeper() {
	// 使用定时器在2秒后启动扫雷游戏
	go func() {
		time.Sleep(time.Duration(GameStartDelayTime) * time.Second)

		// 检查房间是否还存在且未关闭
		if slf.isClosed || slf.isStopped {
			logx.Infof("房间已关闭，取消扫雷游戏启动 RoomID:%v", slf.RoomID)
			return
		}

		// 切换到扫雷状态
		slf.switchToMinesweeping()
	}()

	logx.Infof("扫雷游戏启动已安排，%d秒后开始 RoomID:%v", GameStartDelayTime, slf.RoomID)
}

// switchToMinesweeping 切换到扫雷状态
func (slf *Room) switchToMinesweeping() {
	slf.gameStatus = constvar.GameStatusMinesweeping
	slf.countDown = MinesweeperRoundTime

	// 确保扫雷地图已初始化（在Start方法中可能已经初始化）
	if slf.mineMap == nil {
		slf.mineMap = NewMineMap(slf.RoomID)
	}

	// 初始化回合操作管理
	slf.currentRound = 1
	slf.roundActions = make(map[string]*RoundAction)
	slf.roundStartTime = time.Now().Unix()
	slf.firstPlayerInRound = "" // 重置首选玩家

	// 初始化玩家总分
	slf.playerTotalScores = make(map[string]int)
	for _, user := range slf.allUser {
		if user.Pos >= 0 { // 只初始化在座位上的玩家
			slf.playerTotalScores[user.UserID] = 0
		}
	}

	// 直接发送回合开始通知，开始游戏
	slf.broadcastRoundStart()

	logx.Infof("切换到扫雷状态成功，游戏正式开始 RoomID:%v MapSize:%dx%d MineCount:%d Round:%d",
		slf.RoomID, slf.mineMap.Width, slf.mineMap.Height, slf.mineMap.MineCount, slf.currentRound)
}

// broadcastRoundStart 广播回合开始通知
func (slf *Room) broadcastRoundStart() {
	// 只发送回合相关信息，地图配置已在NoticeStartGame中发送
	slf.Broadcast(constvar.MsgTypeNoticeRoundStart, ecode.OK, map[string]interface{}{
		"roundNumber": slf.currentRound,
		"countDown":   slf.countDown,
		"gameStatus":  slf.gameStatus,
	})

	logx.Infof("回合开始通知发送成功 RoomID:%v CountDown:%v Round:%d",
		slf.RoomID, slf.countDown, slf.currentRound)
}

// calculateRoundScores 计算当前回合所有玩家的基础得分（用于回合结束时的NoticeRoundEnd消息）
func (slf *Room) calculateRoundScores() {
	if slf.mineMap == nil {
		logx.Errorf("地图未初始化，无法计算得分 RoomID:%v", slf.RoomID)
		return
	}

	// 计算基础得分并更新总分
	for userID, action := range slf.roundActions {
		// 计算基础得分（挖掘/标记的得分，不包含首选奖励）
		baseScore := slf.mineMap.CalculateActionScore(action)

		// 设置action的得分为基础得分（用于NoticeRoundEnd消息，不包含首选奖励）
		action.Score = baseScore

		// 更新玩家总分（只加基础得分，首选奖励已经在操作时加过了）
		if _, exists := slf.playerTotalScores[userID]; !exists {
			slf.playerTotalScores[userID] = 0
		}
		slf.playerTotalScores[userID] += baseScore

		logx.Infof("回合基础得分计算 RoomID:%v Round:%d UserID:%v BaseScore:%d TotalScore:%d",
			slf.RoomID, slf.currentRound, userID, baseScore, slf.playerTotalScores[userID])
	}
}

// startNewRound 开始新回合（为后续扩展预留）
func (slf *Room) startNewRound() {
	slf.currentRound++
	slf.roundActions = make(map[string]*RoundAction)
	slf.roundStartTime = time.Now().Unix()
	slf.firstPlayerInRound = "" // 重置首选玩家

	slf.broadcastRoundStart()

	logx.Infof("新回合开始 RoomID:%v Round:%d", slf.RoomID, slf.currentRound)
}

// performAIAction AI托管操作（不享受首选奖励）
func (slf *Room) performAIAction(userID string, x, y, action int) {
	// 验证坐标有效性
	if !slf.mineMap.IsValidPosition(x, y) {
		logx.Errorf("AI托管操作坐标无效 RoomID:%v UserID:%v x:%v y:%v", slf.RoomID, userID, x, y)
		return
	}

	// 验证操作类型
	if action != 1 && action != 2 {
		logx.Errorf("AI托管操作类型无效 RoomID:%v UserID:%v action:%v", slf.RoomID, userID, action)
		return
	}

	// 注意：AI托管时不更新 firstPlayerInRound，因此不享受首选奖励
	slf.roundActions[userID] = &RoundAction{
		UserID:    userID,
		X:         x,
		Y:         y,
		Action:    action,
		Timestamp: time.Now().Unix(),
		Score:     0, // 回合结束时计算
	}

	logx.Infof("AI托管操作执行 RoomID:%v Round:%d UserID:%v x:%v y:%v action:%v",
		slf.RoomID, slf.currentRound, userID, x, y, action)
}

// performAIForInactivePlayers 为未操作的玩家执行AI托管（系统随机选择一格）
func (slf *Room) performAIForInactivePlayers() {
	if slf.mineMap == nil {
		logx.Errorf("地图未初始化，无法执行AI托管 RoomID:%v", slf.RoomID)
		return
	}

	// 找出所有未操作的玩家
	inactivePlayers := make([]string, 0)
	for _, user := range slf.allUser {
		if user.Pos >= 0 { // 只处理在座位上的玩家
			if _, hasAction := slf.roundActions[user.UserID]; !hasAction {
				inactivePlayers = append(inactivePlayers, user.UserID)
			}
		}
	}

	if len(inactivePlayers) == 0 {
		logx.Infof("所有玩家都已操作，无需AI托管 RoomID:%v Round:%d", slf.RoomID, slf.currentRound)
		return
	}

	// 为每个未操作的玩家随机选择一格
	for _, userID := range inactivePlayers {
		x, y, action := slf.generateRandomAction()
		slf.performAIAction(userID, x, y, action)

		logx.Infof("AI托管为未操作玩家随机选择 RoomID:%v Round:%d UserID:%v x:%v y:%v action:%v",
			slf.RoomID, slf.currentRound, userID, x, y, action)
	}

	logx.Infof("AI托管完成 RoomID:%v Round:%d InactivePlayers:%d",
		slf.RoomID, slf.currentRound, len(inactivePlayers))
}

// generateRandomAction 生成随机的AI操作
func (slf *Room) generateRandomAction() (int, int, int) {
	if slf.mineMap == nil {
		// 异常情况下返回默认值
		return 0, 0, 1
	}

	// 收集所有可用的格子（未被挖掘的格子）
	availableBlocks := make([][2]int, 0)
	for y := 0; y < slf.mineMap.Height; y++ {
		for x := 0; x < slf.mineMap.Width; x++ {
			block := &slf.mineMap.Blocks[y][x]
			if !block.IsRevealed {
				availableBlocks = append(availableBlocks, [2]int{x, y})
			}
		}
	}

	// 如果没有可用格子，随机选择一个格子
	if len(availableBlocks) == 0 {
		x := slf.Rand(0, slf.mineMap.Width-1)
		y := slf.Rand(0, slf.mineMap.Height-1)
		action := slf.Rand(1, 2) // 随机选择挖掘(1)或标记(2)
		return x, y, action
	}

	// 从可用格子中随机选择一个
	randomIndex := slf.Rand(0, len(availableBlocks)-1)
	selectedBlock := availableBlocks[randomIndex]
	x := selectedBlock[0]
	y := selectedBlock[1]

	// 随机选择操作类型：70%概率挖掘，30%概率标记
	var action int
	if slf.Rand(1, 100) <= 70 {
		action = 1 // 挖掘
	} else {
		action = 2 // 标记
	}

	return x, y, action
}

// pushFirstChoiceBonusIfApplicable 如果是首选玩家，推送首选玩家奖励
func (slf *Room) pushFirstChoiceBonusIfApplicable(userID string) {
	// 检查是否为首选玩家
	isFirstChoice := (userID == slf.firstPlayerInRound)
	if !isFirstChoice {
		return
	}

	// 首选玩家奖励固定为+1分
	bonusScore := 1

	// 更新玩家总分（只加首选奖励）
	if _, exists := slf.playerTotalScores[userID]; !exists {
		slf.playerTotalScores[userID] = 0
	}
	slf.playerTotalScores[userID] += bonusScore

	// 构建首选玩家奖励数据
	bonusUpdate := &PlayerFirstChoiceBonus{
		UserID:      userID,
		RoundNumber: slf.currentRound,
		BonusScore:  bonusScore,
		TotalScore:  slf.playerTotalScores[userID],
	}

	// 发送首选玩家奖励推送
	slf.NotifyTo(userID, constvar.MsgTypeNoticeFirstChoiceBonus, ecode.OK, bonusUpdate)

	logx.Infof("首选玩家奖励推送 RoomID:%v Round:%d UserID:%v BonusScore:%d TotalScore:%d",
		slf.RoomID, slf.currentRound, userID, bonusScore, slf.playerTotalScores[userID])
}

// SwitchToRoundEnd 切换到回合结束状态
func (slf *Room) SwitchToRoundEnd() {
	slf.gameStatus = constvar.GameStatusRoundEnd
	slf.countDown = RoundEndDisplayTime // 5秒展示时间

	// 计算本回合得分
	slf.calculateRoundScores()

	// 更新地图状态（揭示玩家选择的方块）
	slf.updateMapWithPlayerActions()

	// 广播回合结束通知
	slf.broadcastRoundEnd()

	logx.Infof("切换到回合结束状态 RoomID:%v Round:%d CountDown:%d",
		slf.RoomID, slf.currentRound, slf.countDown)
}

// updateMapWithPlayerActions 更新地图状态，揭示玩家操作的方块
func (slf *Room) updateMapWithPlayerActions() {
	if slf.mineMap == nil {
		logx.Errorf("地图未初始化，无法更新地图状态 RoomID:%v", slf.RoomID)
		return
	}

	for userID, action := range slf.roundActions {
		block := &slf.mineMap.Blocks[action.Y][action.X]

		if action.Action == 1 { // 挖掘操作
			if !block.IsRevealed {
				block.IsRevealed = true
				slf.mineMap.RevealedCount++
				logx.Infof("方块被挖掘 RoomID:%v Round:%d UserID:%v x:%v y:%v IsMine:%v",
					slf.RoomID, slf.currentRound, userID, action.X, action.Y, block.IsMine)
			}
		} else if action.Action == 2 { // 标记操作
			block.IsMarked = !block.IsMarked // 切换标记状态
			logx.Infof("方块标记状态切换 RoomID:%v Round:%d UserID:%v x:%v y:%v IsMarked:%v",
				slf.RoomID, slf.currentRound, userID, action.X, action.Y, block.IsMarked)
		}

		// 记录该格子中的玩家（避免重复添加）
		found := false
		for _, playerID := range block.Players {
			if playerID == userID {
				found = true
				break
			}
		}
		if !found {
			block.Players = append(block.Players, userID)
		}
	}

	logx.Infof("地图状态更新完成 RoomID:%v Round:%d RevealedCount:%d ActionsCount:%d",
		slf.RoomID, slf.currentRound, slf.mineMap.RevealedCount, len(slf.roundActions))
}

// broadcastRoundEnd 广播回合结束通知
func (slf *Room) broadcastRoundEnd() {
	// 构建玩家操作结果
	playerResults := make([]*PlayerRoundResult, 0)
	for userID, action := range slf.roundActions {
		playerResults = append(playerResults, &PlayerRoundResult{
			UserID:        userID,
			X:             action.X,
			Y:             action.Y,
			Action:        action.Action,
			Score:         action.Score,
			IsFirstChoice: userID == slf.firstPlayerInRound,
		})
	}

	// 构建地图数据（只发送已揭示的方块信息）
	mapData := slf.buildMapDataForClient()

	slf.Broadcast(constvar.MsgTypeNoticeRoundEnd, ecode.OK, map[string]interface{}{
		"roundNumber":   slf.currentRound,
		"gameStatus":    slf.gameStatus,
		"countDown":     slf.countDown,
		"playerResults": playerResults,
		"mapData":       mapData,
	})

	logx.Infof("回合结束通知发送成功 RoomID:%v Round:%d PlayerResults:%d",
		slf.RoomID, slf.currentRound, len(playerResults))
}

// buildMapDataForClient 构建发送给客户端的地图数据
func (slf *Room) buildMapDataForClient() [][]map[string]interface{} {
	if slf.mineMap == nil {
		return nil
	}

	mapData := make([][]map[string]interface{}, slf.mineMap.Height)
	for y := 0; y < slf.mineMap.Height; y++ {
		mapData[y] = make([]map[string]interface{}, slf.mineMap.Width)
		for x := 0; x < slf.mineMap.Width; x++ {
			block := &slf.mineMap.Blocks[y][x]
			blockData := map[string]interface{}{
				"x":          block.X,
				"y":          block.Y,
				"isRevealed": block.IsRevealed,
				"isMarked":   block.IsMarked,
				"players":    block.Players,
			}

			// 只有已揭示的方块才发送地雷信息和周围地雷数
			if block.IsRevealed {
				blockData["isMine"] = block.IsMine
				blockData["neighborMines"] = block.NeighborMines
			}

			mapData[y][x] = blockData
		}
	}

	return mapData
}

// checkGameEndConditions 检查游戏是否应该结束
func (slf *Room) checkGameEndConditions() bool {
	if slf.mineMap == nil {
		logx.Errorf("地图未初始化，无法检查游戏结束条件 RoomID:%v", slf.RoomID)
		return true // 异常情况下结束游戏
	}

	// 条件1：所有非地雷格子都被至少一个玩家挖掘过
	allSafeBlocksRevealed := slf.mineMap.IsGameComplete()

	// 条件2：剩余地雷都标记准确
	allMinesMarkedCorrectly := slf.checkAllMinesMarkedCorrectly()

	if allSafeBlocksRevealed {
		logx.Infof("游戏完成：所有安全区已被挖掘 RoomID:%v RevealedCount:%d",
			slf.RoomID, slf.mineMap.RevealedCount)
		return true
	}

	if allMinesMarkedCorrectly {
		logx.Infof("游戏完成：剩余地雷都标记准确 RoomID:%v", slf.RoomID)
		return true
	}

	logx.Infof("游戏继续 RoomID:%v RevealedCount:%d TotalSafeBlocks:%d AllMinesMarked:%v",
		slf.RoomID, slf.mineMap.RevealedCount,
		slf.mineMap.Width*slf.mineMap.Height-slf.mineMap.MineCount, allMinesMarkedCorrectly)

	return false
}

// checkAllMinesMarkedCorrectly 检查剩余地雷是否都标记准确
func (slf *Room) checkAllMinesMarkedCorrectly() bool {
	if slf.mineMap == nil {
		return false
	}

	// 统计未挖掘的地雷和非地雷格子
	unrevealedMines := 0         // 未挖掘的地雷数量
	unrevealedSafeBlocks := 0    // 未挖掘的安全区数量
	correctlyMarkedMines := 0    // 正确标记的地雷数量
	incorrectlyMarkedBlocks := 0 // 错误标记的方块数量

	for y := 0; y < slf.mineMap.Height; y++ {
		for x := 0; x < slf.mineMap.Width; x++ {
			block := &slf.mineMap.Blocks[y][x]

			if !block.IsRevealed {
				if block.IsMine {
					unrevealedMines++
					if block.IsMarked {
						correctlyMarkedMines++
					}
				} else {
					unrevealedSafeBlocks++
					if block.IsMarked {
						incorrectlyMarkedBlocks++ // 安全区被错误标记为地雷
					}
				}
			}
		}
	}

	// 游戏完成条件：
	// 1. 所有未挖掘的地雷都被正确标记
	// 2. 没有安全区被错误标记为地雷
	// 3. 所有安全区都已被挖掘（这个条件在IsGameComplete中检查）
	allUnrevealedMinesMarked := (unrevealedMines > 0 && correctlyMarkedMines == unrevealedMines)
	noIncorrectMarks := (incorrectlyMarkedBlocks == 0)

	logx.Infof("地雷标记检查 RoomID:%v UnrevealedMines:%d CorrectlyMarked:%d IncorrectMarks:%d UnrevealedSafe:%d",
		slf.RoomID, unrevealedMines, correctlyMarkedMines, incorrectlyMarkedBlocks, unrevealedSafeBlocks)

	return allUnrevealedMinesMarked && noIncorrectMarks
}

// SwitchToGameEnd 切换到游戏结束状态
func (slf *Room) SwitchToGameEnd() {
	slf.gameStatus = constvar.GameStatusGameEnd
	slf.countDown = GameEndDisplayTime // 10秒结算展示时间

	// 计算最终排名
	finalRanking := slf.calculateFinalRanking()

	// 广播游戏结束通知
	slf.broadcastGameEnd(finalRanking)

	// TODO: 更新玩家数据（金币、经验等）- 后续实现
	// slf.updatePlayerStats(finalRanking)

	logx.Infof("切换到游戏结束状态 RoomID:%v CountDown:%d FinalRanking:%d",
		slf.RoomID, slf.countDown, len(finalRanking))
}

// calculateFinalRanking 计算最终排名（使用累计总分）
func (slf *Room) calculateFinalRanking() []*PlayerFinalResult {
	// 转换为排名列表，使用已累计的总分
	ranking := make([]*PlayerFinalResult, 0)
	for userID, totalScore := range slf.playerTotalScores {
		ranking = append(ranking, &PlayerFinalResult{
			UserID:     userID,
			TotalScore: totalScore,
			Rank:       0, // 稍后计算
		})
	}

	// 按得分排序（得分高的排名靠前）
	for i := 0; i < len(ranking)-1; i++ {
		for j := i + 1; j < len(ranking); j++ {
			if ranking[i].TotalScore < ranking[j].TotalScore {
				ranking[i], ranking[j] = ranking[j], ranking[i]
			}
		}
	}

	// 设置排名（处理并列情况）
	currentRank := 1
	for i := 0; i < len(ranking); i++ {
		if i > 0 && ranking[i].TotalScore < ranking[i-1].TotalScore {
			currentRank = i + 1
		}
		ranking[i].Rank = currentRank
	}

	logx.Infof("最终排名计算完成 RoomID:%v PlayerCount:%d", slf.RoomID, len(ranking))
	for _, result := range ranking {
		logx.Infof("排名详情 RoomID:%v UserID:%v Rank:%d TotalScore:%d",
			slf.RoomID, result.UserID, result.Rank, result.TotalScore)
	}

	return ranking
}

// broadcastGameEnd 广播游戏结束通知
func (slf *Room) broadcastGameEnd(finalRanking []*PlayerFinalResult) {
	// 构建完整的地图数据（游戏结束时显示所有信息）
	completeMapData := slf.buildCompleteMapDataForClient()

	slf.Broadcast(constvar.MsgTypeNoticeGameEnd, ecode.OK, map[string]interface{}{
		"gameStatus":      slf.gameStatus,
		"countDown":       slf.countDown,
		"finalRanking":    finalRanking,
		"completeMapData": completeMapData,
		"totalRounds":     slf.currentRound,
	})

	logx.Infof("游戏结束通知发送成功 RoomID:%v TotalRounds:%d FinalRanking:%d",
		slf.RoomID, slf.currentRound, len(finalRanking))
}

// buildCompleteMapDataForClient 构建完整的地图数据（游戏结束时使用）
func (slf *Room) buildCompleteMapDataForClient() [][]map[string]interface{} {
	if slf.mineMap == nil {
		return nil
	}

	mapData := make([][]map[string]interface{}, slf.mineMap.Height)
	for y := 0; y < slf.mineMap.Height; y++ {
		mapData[y] = make([]map[string]interface{}, slf.mineMap.Width)
		for x := 0; x < slf.mineMap.Width; x++ {
			block := &slf.mineMap.Blocks[y][x]
			blockData := map[string]interface{}{
				"x":             block.X,
				"y":             block.Y,
				"isMine":        block.IsMine, // 游戏结束时显示所有地雷
				"isRevealed":    block.IsRevealed,
				"isMarked":      block.IsMarked,
				"neighborMines": block.NeighborMines, // 显示所有周围地雷数
				"players":       block.Players,
			}

			mapData[y][x] = blockData
		}
	}

	return mapData
}

// broadcastActionDisplay 广播玩家操作展示（第20秒展示阶段，AI托管后）
func (slf *Room) broadcastActionDisplay() {
	// 构建所有玩家的操作展示数据
	playerActions := make([]*PlayerActionDisplay, 0)

	// 为所有在座位上的玩家构建展示数据
	// 注意：此时AI托管已经执行，所有玩家都应该有操作记录
	for _, user := range slf.allUser {
		if user.Pos >= 0 { // 只处理在座位上的玩家
			if action, hasAction := slf.roundActions[user.UserID]; hasAction {
				// 所有玩家都应该有操作（包括真实操作和AI托管）
				playerActions = append(playerActions, &PlayerActionDisplay{
					UserID: user.UserID,
					X:      action.X,
					Y:      action.Y,
					Action: action.Action,
				})
			} else {
				// 理论上不应该出现这种情况，因为AI托管已经执行
				logx.Errorf("玩家在AI托管后仍无操作记录 RoomID:%v UserID:%v", slf.RoomID, user.UserID)
			}
		}
	}

	// 广播操作展示
	slf.Broadcast(constvar.MsgTypeNoticeActionDisplay, ecode.OK, map[string]interface{}{
		"roundNumber":   slf.currentRound,
		"gameStatus":    slf.gameStatus,
		"countDown":     slf.countDown,
		"playerActions": playerActions,
		"message":       "展示阶段：显示所有玩家操作",
	})

	logx.Infof("操作展示广播完成 RoomID:%v Round:%d PlayerActions:%d",
		slf.RoomID, slf.currentRound, len(playerActions))
}
