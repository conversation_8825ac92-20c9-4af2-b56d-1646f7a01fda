package roommgr

// 扫雷游戏相关时间常量
const (
	GameStartDelayTime   = 2  // 游戏开始延迟时间(秒)
	MinesweeperRoundTime = 25 // 扫雷回合时间(秒)
	MinesweeperShowTime  = 20 // 前20秒隐藏其他人选择，第20秒后显示所有人选择
	RoundEndDisplayTime  = 5  // 回合结束展示时间(秒)
	GameEndDisplayTime   = 10 // 游戏结束展示时间(秒)
)

type OpBy string

const (
	OpByClient  OpBy = "opByClient"
	OpByRobot   OpBy = "opByRobot"
	OpByTimeout OpBy = "opByTimeout"
)

type Side int

const (
	LeftSide  = Side(1) // 左边
	RightSide = Side(2) // 右边
)
