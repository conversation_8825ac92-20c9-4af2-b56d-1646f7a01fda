package conf

import (
	"connect/common/gormx"
	"connect/common/logx"
	"connect/common/redisx"
	"connect/constvar"
	"flag"
	"github.com/spf13/viper"
)

var (
	// config file name
	configName = "connect.snakes.local"
	// config file paths
	configPaths = []string{
		"./",
		"./config",
	}
)

type (
	ServerConf struct {
		ID          string
		GameId      constvar.GameID
		RoutePrefix string // 路由前缀
		Project     string // 一个项目名称可以对应多个serverID
		Port        string
		Env         int
		FeiShu      string
	}

	BobiConf struct {
		Develop    string
		PreRelease string
		Release    string
	}

	config struct {
		// 服务器配置
		Server ServerConf

		// mysql配置
		Mysql gormx.GormConf

		// redis用户配置
		Redis redisx.RedisConf

		// Bobi配置
		Bobi BobiConf
	}
)

var Conf = new(config)

func Init() error {
	// 取消通过flag指定配置文件名称
	var configFile string
	flag.StringVar(&configFile, "config", "", "config file in json")
	flag.Parse()
	if len(configFile) > 0 {
		configName = configFile
	}

	viper.SetConfigName(configName) // 配置文件名称(无扩展名)
	viper.SetConfigType("json")     // 如果配置文件的名称中没有扩展名，则需要指定
	for _, path := range configPaths {
		viper.AddConfigPath(path)
	}

	Conf.Reload()
	return nil
}

func (c *config) Reload() {
	if err := viper.ReadInConfig(); err != nil {
		logx.Infof("ReadInConfig err:%v", err)
	}

	if err := viper.Unmarshal(c); err != nil {
		logx.Infof("Unmarshal err:%v", err)
	}

	c.Show()
}

func (c *config) Show() {
	logx.Info("......................................................")
	logx.Infof("   Server:         %+v", c.Server)
	logx.Infof("   Mysql:          %+v", c.Mysql)
	logx.Infof("   Redis:          %+v", c.Redis)
	logx.Infof("   Bobi:           %+v", c.Bobi)
	logx.Info("......................................................")
}

func IsDebug() bool {
	return Conf.Server.Env == 0 || Conf.Server.Env == 1
}
