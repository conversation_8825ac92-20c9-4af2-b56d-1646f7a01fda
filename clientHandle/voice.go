package clientHandle

import (
	"connect/common/logx"
	"connect/common/msg"
	"connect/common/websocketx"
	"connect/conf"
	"connect/ecode"
	"connect/gameservice"
	"connect/model/common/base"
	"connect/model/common/request"
	"connect/model/dao"
	"connect/usermgr"
	"connect/utils"
	"github.com/mitchellh/mapstructure"
)

// OnVoiceChangeRole 玩家变更角色
func (h *Handle) OnVoiceChangeRole(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	params := &request.VoiceChangeRole{}
	err := mapstructure.Decode(clientMsg.Data, params)
	if err != nil {
		logx.Errorf("OnVoiceChangeRole mapstructure.Decode failed, err:%v", err)
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}
	if !conf.Conf.Server.GameId.IsVoice() {
		logx.Errorf("OnVoiceChangeRole not voice netID:%v", socket.GetNetID())
		return
	}

	user.ChangeRole(params.Role)
	if len(user.VoiceSrvID) == 0 {
		voiceInfo, _ := dao.GroupDao.VoiceInfo.Get(user.AppChannel, user.AppID, user.PlatRoomID)
		if len(voiceInfo.SrvID) <= 0 {
			logx.Infof("VoiceInfo no find PlatRoomID:%v", user.PlatRoomID)
			utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundVoiceRoom)
			return
		}
		user.UpdateVoiceInfo(voiceInfo, "user.VoiceSrvID empty")
	}

	// user.VoiceSrvID对应的服务器是否已经死掉
	if len(user.VoiceSrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(user.VoiceSrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", user.VoiceSrvID, user.UserID)
		dao.GroupDao.VoiceInfo.Delete(user.AppChannel, user.AppID, user.PlatRoomID)
		user.UpdateVoiceInfo(&dao.VoiceInfo{}, "gameSrv not health")
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundVoiceRoom)
		return
	}
	logx.Infof("OnVoiceChangeRole serverId:%v user:%v role:%v", user.SrvID, user.UserID, params.Role)

	_ = dao.GroupDao.GameReqList.Add(user.VoiceSrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data:       *clientMsg,
	})
}
