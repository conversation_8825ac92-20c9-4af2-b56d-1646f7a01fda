package clientHandle

import (
	"connect/common/iface"
	"connect/common/logx"
	"connect/common/msg"
	"connect/common/websocketx"
	"connect/conf"
	"connect/constvar"
	"connect/ecode"
	"connect/gameservice"
	"connect/model/common/base"
	"connect/model/dao"
	"connect/usermgr"
	"connect/utils"
	"encoding/json"
	"strings"
	"time"
)

type Handle struct{}

var handler = new(Handle)

func GetHandler() iface.IAcceptorHandle {
	return handler
}

func (h *Handle) OnMessage(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := &msg.FromClientMsg{}
	err := json.Unmarshal(netData, clientMsg)
	if err != nil {
		return
	}

	if clientMsg.MsgID == constvar.MsgTypeHeartbeat {
		h.OnHeartbeat(socket, clientMsg.Data)
		return
	}
	logx.Infof("===OnMessage userID:%v, netID:%v, Msg:%v, UnixMilli:%v", socket.GetUserID(), socket.GetNetID(), string(netData), time.Now().UnixMilli())

	switch clientMsg.MsgID {
	case constvar.MsgTypeLogin:
		h.OnRequestLogin(socket, clientMsg) // 玩家登录
	case constvar.MsgTypeUserInfo:
		h.OnRequestSelfInfo(socket, clientMsg) // 获取用户信息(登录时不请求，app回调前端币变化时，前端才请求)
	case constvar.MsgTypePairRequest:
		h.OnPairRequest(socket, clientMsg) // 请求匹配
	case constvar.MsgTypeCancelPair:
		h.OnCancelPair(socket, clientMsg) // 取消匹配
	case constvar.MsgTypeCreateInvite:
		h.OnCreateInvite(socket, clientMsg) // 创建邀请
	case constvar.MsgTypeAcceptInvite:
		h.OnAcceptInvite(socket, clientMsg) // 接受邀请
	case constvar.MsgTypeVoiceChangeRole:
		h.OnVoiceChangeRole(socket, clientMsg) // 玩家变更角色
	case constvar.MsgTypeProductList, constvar.MsgTypeBuyProduct, constvar.MsgTypeSetSkin:
		h.OnShopMsg(socket, clientMsg) // 处理商城消息
	default:
		if strings.Contains(clientMsg.MsgID, "Voice") {
			h.OnVoiceMsg(socket, clientMsg)
			return
		} else if strings.Contains(clientMsg.MsgID, "Invite") {
			h.OnInviteMsg(socket, clientMsg) // 处理其它邀请消息
			return
		} else if strings.Contains(clientMsg.MsgID, "Extend") {
			h.OnExtendMsg(socket, clientMsg) // 处理扩展消息
			return
		}
		h.OnGameMsg(socket, clientMsg) // 游戏中消息
	}
}

// OnHeartbeat 心跳消息
func (h *Handle) OnHeartbeat(socket *websocketx.WsSocket, data interface{}) {
	if data != nil {
		v, ok := data.(map[string]interface{})
		if ok {
			// 给 ServerTime 字段赋值当前服务器时间（Unix 毫秒时间戳）
			v["serverTime"] = time.Now().UnixMilli()
			utils.OkWithDetailed(socket, constvar.MsgTypeHeartbeat, v)
			return
		}
	}
	utils.OkWithDetailed(socket, constvar.MsgTypeHeartbeat, map[string]interface{}{
		"serverTime": time.Now().UnixMilli(),
	})
}

// OnVoiceMsg 配桌消息
func (h *Handle) OnVoiceMsg(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("OnVoiceMsg no find netID:%v", socket.GetNetID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if !conf.Conf.Server.GameId.IsVoice() {
		logx.Errorf("OnVoiceMsg not voice netID:%v", socket.GetNetID())
		return
	}

	if len(user.VoiceSrvID) == 0 {
		voiceInfo, _ := dao.GroupDao.VoiceInfo.Get(user.AppChannel, user.AppID, user.PlatRoomID)
		if len(voiceInfo.SrvID) <= 0 {
			logx.Infof("VoiceInfo no find PlatRoomID:%v", user.PlatRoomID)
			utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundVoiceRoom)
			return
		}
		user.UpdateVoiceInfo(voiceInfo, "user.VoiceSrvID empty")
	}

	// user.VoiceSrvID对应的服务器是否已经死掉
	if len(user.VoiceSrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(user.VoiceSrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", user.VoiceSrvID, user.UserID)
		dao.GroupDao.VoiceInfo.Delete(user.AppChannel, user.AppID, user.PlatRoomID)
		user.UpdateVoiceInfo(&dao.VoiceInfo{}, "gameSrv not health")
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundVoiceRoom)
		return
	}

	_ = dao.GroupDao.GameReqList.Add(user.VoiceSrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data:       *clientMsg,
	})
}

// OnGameMsg 游戏中消息
func (h *Handle) OnGameMsg(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("OnGameMsg no find netID:%v", socket.GetNetID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	var srvID string
	if conf.Conf.Server.GameId.IsVoice() {
		if len(user.VoiceSrvID) == 0 {
			voiceInfo, _ := dao.GroupDao.VoiceInfo.Get(user.AppChannel, user.AppID, user.PlatRoomID)
			if len(voiceInfo.SrvID) <= 0 {
				utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundVoiceRoom)
				return
			}
			user.UpdateVoiceInfo(voiceInfo, "user.VoiceSrvID empty")
		}

		// user.VoiceSrvID对应的服务器是否已经死掉
		if len(user.VoiceSrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(user.VoiceSrvID) {
			logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", user.VoiceSrvID, user.UserID)
			dao.GroupDao.VoiceInfo.Delete(user.AppChannel, user.AppID, user.PlatRoomID)
			user.UpdateVoiceInfo(&dao.VoiceInfo{}, "gameSrv not health")
			utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundVoiceRoom)
			return
		}
		srvID = user.VoiceSrvID
	} else {
		if len(user.SrvID) == 0 {
			location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
			if len(location.SrvID) <= 0 {
				utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundRoom)
				return
			}
			user.UpdateLocation(location, "user.SrvID empty")
		}

		// user.SrvID对应的服务器是否已经死掉
		if len(user.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(user.SrvID) {
			logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", user.SrvID, user.UserID)
			dao.GroupDao.UserLocation.Delete(user.AppChannel, user.AppID, user.UserID)
			user.UpdateLocation(&dao.UserLocation{}, "gameSrv not health")
			utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundRoom)
			return
		}
		srvID = user.SrvID
	}
	if len(srvID) <= 0 {
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundRoom)
		return
	}

	var reqMsg = &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data:       *clientMsg,
	}
	if clientMsg.MsgID == constvar.MsgTypeEnterRoom {
		reqMsg.User = user.GetGameUser()
	}
	_ = dao.GroupDao.GameReqList.Add(srvID, reqMsg)
}
