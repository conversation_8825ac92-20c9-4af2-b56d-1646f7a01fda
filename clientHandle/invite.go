package clientHandle

import (
	"connect/common/logx"
	"connect/common/msg"
	"connect/common/websocketx"
	"connect/conf"
	"connect/ecode"
	"connect/gameservice"
	"connect/model/common/base"
	"connect/model/common/request"
	"connect/model/dao"
	"connect/usermgr"
	"connect/utils"
	"github.com/mitchellh/mapstructure"
)

// OnCreateInvite 创建邀请
func (h *Handle) OnCreateInvite(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	// 判断玩家是否已经在游戏中
	location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
	if location.RoomID > 0 {
		logx.Infof("userID:%v isPlaying location:%+v", user.UserID, location)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrPlaying)
		return
	} else if len(location.SrvID) > 0 {
		logx.Infof("userID:%v isPairing, location:%+v", user.UserID, location)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrInPair)
		return
	}

	// 判断玩家是否已经在邀请中
	invite, _ := dao.GroupDao.UserInvite.Get(user.AppChannel, user.AppID, user.UserID)
	// invite.SrvID对应的服务器是否已经死掉
	if len(invite.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(invite.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", invite.SrvID, user.UserID)
		dao.GroupDao.InviteInfo.Delete(invite.InviteCode)
		dao.GroupDao.UserInvite.Delete(user.AppChannel, user.AppID, user.UserID)
		logx.Infof("OnCreateInvite delete inviteCode:%v srvID:%v not health", invite.InviteCode, invite.SrvID)
		invite = &dao.UserInvite{}
	}

	// 玩家选择一个游戏服务器
	if len(invite.SrvID) <= 0 {
		gameService, errCode := gameservice.GetInstance().GetUserGameService(user.UserID)
		if errCode != ecode.OK {
			logx.Errorf("userID:%v GetUserGameService errCode:%v", user.UserID, errCode)
			utils.Fail(socket, clientMsg.MsgID, errCode)
			return
		}
		invite.SrvID = gameService.SrvID
	}

	_ = dao.GroupDao.GameReqList.Add(invite.SrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data:       *clientMsg,
		User:       user.GetGameUser(),
	})
}

// OnAcceptInvite 接受邀请
func (h *Handle) OnAcceptInvite(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	params := &request.AcceptInvite{}
	err := mapstructure.Decode(clientMsg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	// 判断玩家是否已经在游戏中
	location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
	if location.RoomID > 0 {
		logx.Infof("userID:%v isPlaying location:%+v", user.UserID, location)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrPlaying)
		return
	} else if len(location.SrvID) > 0 {
		logx.Infof("userID:%v isPairing, location:%+v", user.UserID, location)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrInPair)
		return
	}

	// 判断玩家是否已经在邀请中
	invite, _ := dao.GroupDao.UserInvite.Get(user.AppChannel, user.AppID, user.UserID)
	if invite.InviteCode > 0 && invite.InviteCode != params.InviteCode {
		logx.Infof("userID:%v isInviting oldInviteCode:%v", user.UserID, invite.InviteCode)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrInvalidInviteCode)
		return
	}

	// 获取邀请信息
	inviteInfo, _ := dao.GroupDao.InviteInfo.Get(params.InviteCode)
	if len(inviteInfo.SrvID) <= 0 { // 邀请码不存在
		logx.Infof("userID:%v invalid inviteCode:%v", user.UserID, params.InviteCode)
		dao.GroupDao.UserInvite.Delete(user.AppChannel, user.AppID, user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrInvalidInviteCode)
		return
	}

	// inviteInfo.SrvID对应的服务器是否已经死掉
	if !gameservice.GetInstance().IsSrvHealth(inviteInfo.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", inviteInfo.SrvID, user.UserID)
		dao.GroupDao.InviteInfo.Delete(params.InviteCode)
		dao.GroupDao.UserInvite.Delete(user.AppChannel, user.AppID, user.UserID)
		logx.Infof("OnAcceptInvite delete inviteCode:%v srvID:%v not health", invite.InviteCode, invite.SrvID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrInvalidInviteCode)
		return
	}

	_ = dao.GroupDao.GameReqList.Add(inviteInfo.SrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data:       *clientMsg,
		User:       user.GetGameUser(),
	})
}

// OnInviteMsg 处理其它邀请消息
func (h *Handle) OnInviteMsg(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	// 判断玩家是否已经在邀请中
	invite, _ := dao.GroupDao.UserInvite.Get(user.AppChannel, user.AppID, user.UserID)
	if invite.InviteCode <= 0 { // 未在邀请当中
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotInvite)
		return
	}

	// inviteInfo.SrvID对应的服务器是否已经死掉
	if !gameservice.GetInstance().IsSrvHealth(invite.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", invite.SrvID, user.UserID)
		dao.GroupDao.InviteInfo.Delete(invite.InviteCode)
		dao.GroupDao.UserInvite.Delete(user.AppChannel, user.AppID, user.UserID)
		logx.Infof("OnInviteMsg delete inviteCode:%v srvID:%v not health", invite.InviteCode, invite.SrvID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotInvite)
		return
	}

	_ = dao.GroupDao.GameReqList.Add(invite.SrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data:       *clientMsg,
	})
}
