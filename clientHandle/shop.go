package clientHandle

import (
	"github.com/mitchellh/mapstructure"
	"minesweep/common/logx"
	"minesweep/common/platform"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/localmgr"
	"minesweep/model"
	"minesweep/model/common/base"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
	"minesweep/model/dao"
	"minesweep/roommgr"
	"minesweep/usermgr"
	"minesweep/utils"
	"minesweep/voiceroom"
	"ms-version.soofun.online/wjl/game_public/types_public"
	"time"
)

// OnRequestProductList 玩家请求商店信息
func (h *Handle) OnRequestProductList(req *base.GameReqMsg) {
	if req.User == nil {
		logx.Errorf("no user param userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrParams)
		return
	}

	user := &usermgr.User{
		UserID:     req.UserID,
		Nickname:   req.User.Nickname,
		Avatar:     req.User.Avatar,
		AppID:      req.AppID,
		AppChannel: req.AppChannel,
		GameMode:   req.User.GameMode,
		Coin:       req.User.Coin,
		IsVisitor:  req.User.IsVisitor,
		SSToken:    req.User.SSToken,
		ClientIP:   req.User.ClientIP,
		PlatRoomID: req.PlatRoomID,
		AllProduct: make(map[constvar.ProductID]bool),
		Role:       req.User.Role,
		ConnSrvID:  req.FromID,
	}
	platform.CacheUserInfo(req.UserID, req.User.Coin, req.AppChannel)
	_ = user.UpdateSkinPackage()

	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	var productList = make([]*response.ProductInfo, 0)
	for _, v := range channelCfg.ProductConfigs {
		productList = append(productList,
			&response.ProductInfo{
				ID:     v.ID,
				Price:  v.Price,
				Type:   v.ID.Type(),
				IsHave: user.IsHaveProduct(v.ID),
			})
	}

	utils.OkWithDetailed(req, &response.ProductList{
		ProductList: productList,
	})
}

// OnBuyProduct 玩家请求购买商品
func (h *Handle) OnBuyProduct(req *base.GameReqMsg) {
	if req.User == nil {
		logx.Errorf("no user param userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrParams)
		return
	}

	params := &request.BuyProduct{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		user = &usermgr.User{
			UserID:     req.UserID,
			Nickname:   req.User.Nickname,
			Avatar:     req.User.Avatar,
			AppID:      req.AppID,
			AppChannel: req.AppChannel,
			GameMode:   req.User.GameMode,
			Coin:       req.User.Coin,
			IsVisitor:  req.User.IsVisitor,
			SSToken:    req.User.SSToken,
			ClientIP:   req.User.ClientIP,
			PlatRoomID: req.PlatRoomID,
			AllProduct: make(map[constvar.ProductID]bool),
			Role:       req.User.Role,
			ConnSrvID:  req.FromID,
		}
		platform.CacheUserInfo(req.UserID, req.User.Coin, req.AppChannel)
		_ = user.UpdateSkinPackage()
	}

	// 判断商品是否存在
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	productCfg := channelCfg.GetProductConf(params.Id)
	if productCfg == nil {
		utils.Fail(req, ecode.ErrNotFoundProduct)
		return
	}

	// 判断是否已经买过该商品了
	if user.IsHaveProduct(params.Id) {
		utils.Fail(req, ecode.ErrAlreadyHaveProduct)
		return
	}

	// 判断钱够不够
	if user.Coin < int64(productCfg.Price) {
		utils.Fail(req, ecode.ErrNotEnoughCoin)
		return
	}

	// 扣除玩家金币
	newCoin, errCode := user.ChangeBalance(int(types_public.ActionEventOne), -int64(productCfg.Price), "skin", "", "skin", channelCfg.CoinType, constvar.CoinChangeTypeBuyProduct, 0)
	if errCode != ecode.OK {
		logx.Errorf("OnBuyProduct UserID:%v 扣除金币失败 errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
	user.NoticeUserCoin()              // 同步玩家金币
	_ = user.AddProduct(productCfg.ID) // 添加商品

	// 保存金币变化记录
	_ = dao.GroupDao.GameRecordPersist.Add(&model.GameRecord{
		AppChannel:  user.AppChannel,
		AppID:       user.AppID,
		UserID:      user.UserID,
		ChangeType:  string(constvar.CoinChangeTypeBuyProduct),
		ChangeCount: -productCfg.Price,
		ChangeTime:  time.Now(),
		ResultCoin:  newCoin,
		GameRank:    int(productCfg.ID), // 商品ID赋值到游戏排名字段上
	})
	utils.OkWithDetailed(req, &response.BuyProduct{Id: productCfg.ID})

	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	if local.RoomID > 0 {
		roommgr.GetInstance().UserBuyProduct(local.RoomID, user.AppChannel, user.AppID, user.UserID, params)
	}
}

// OnSetSkin 设置当前使用的皮肤
func (h *Handle) OnSetSkin(req *base.GameReqMsg) {
	if req.User == nil {
		logx.Errorf("no user param userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrParams)
		return
	}

	params := &request.SetSkin{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		user = &usermgr.User{
			UserID:     req.UserID,
			Nickname:   req.User.Nickname,
			Avatar:     req.User.Avatar,
			AppID:      req.AppID,
			AppChannel: req.AppChannel,
			GameMode:   req.User.GameMode,
			Coin:       req.User.Coin,
			IsVisitor:  req.User.IsVisitor,
			SSToken:    req.User.SSToken,
			ClientIP:   req.User.ClientIP,
			PlatRoomID: req.PlatRoomID,
			AllProduct: make(map[constvar.ProductID]bool),
			Role:       req.User.Role,
			ConnSrvID:  req.FromID,
		}
		platform.CacheUserInfo(req.UserID, req.User.Coin, req.AppChannel)
		_ = user.UpdateSkinPackage()
	}
	if !user.IsHaveProduct(params.Id) {
		utils.Fail(req, ecode.ErrNotHaveSkin)
		return
	}
	_ = user.SetSkin(params.Id)

	utils.OkWithDetailed(req, &response.NoticeSetSkin{
		UserID: user.UserID,
		Type:   user.SkinChessID.Type(),
		Id:     params.Id,
	})

	var roomID int64
	if conf.Conf.Server.GameId == int(constvar.GameIDLive) {
		voiceRoom := voiceroom.GetInstance().GetVoiceRoom(user.AppChannel, user.AppID, user.PlatRoomID)
		if voiceRoom != nil {
			roomID = voiceRoom.RoomID
		}
	} else {
		local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
		roomID = local.RoomID
	}
	if roomID > 0 {
		roommgr.GetInstance().SetSkin(roomID, user.AppChannel, user.AppID, user.UserID, params)
	}
}
