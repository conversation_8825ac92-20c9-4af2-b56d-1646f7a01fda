[program:blkcrush_live_conn]
#跳转到此路径并在那里执行受监督的命令。
directory=/data/soofun_game/blkcrush_live/connect
#要执行的命令，可以是完整可执行文件路径，也可以通过PATH变量计算。可以添加参数
command=/data/soofun_game/blkcrush_live/connect/connect -config connect.blkcrush_live.baiyou_test.json
#设置环境变量VARIABLE=value，要传递给受监督程序的值。
#程序日志文件写入地址,请写绝对路径
stdout_logfile=/data/soofun_game/blkcrush_live/connect/logs/stdout.log
#设置环境变量VARIABLE=value，要传递给受监督程序的值。
#environment=EVIDENCE_CONFIG="config.production"
#执行用户
user=root:root
#在将SIGKILL发送到受监控命令以使其无法正常停止之前等待的时间量。默认值10秒
stopwaitsecs=1800
#失败重启次数，超过这个次数直接将状态置为FATAL，默认值3
startretries=2100000000
#程序启动后需要运行的秒数，以考虑启动成功（将进程从起始状态移动到运行状态）。设置为0表示程序不需要在任何特定时间内保持运行。
startsecs=3
#在停止 Supervised program之后等待（至少）这段时间，然后再次strt它。默认0
restartpause=1
#如果受监控命令死掉，则自动重新运行该命令。值可以是false、true、unexpected。
#false：进程不会自动重启，
#unexpected：当程序退出时的退出码不是exitcodes中定义的时，进程会重启，默认值
#true：进程会无条件重启当退出的时候。
autorestart=true
#终止信号标识，在stop进程时的终止信号，可以使用TERM, HUP, INT, QUIT, KILL, USR1, or USR2，默认空看着像是KILL
#如果配置了多个停止信号，则在停止程序时，主管会以“stopwaitsecs”为间隔将信号一一发送给程序。如果在所有信号发送给程序后程序没有退出，supervisord 将杀死该程>序。
stopsignal=TERM
#STDERR 是否应该重定向到 STDOUT。默认false
redirect_stderr=true