package voiceroom

import (
	"github.com/mitchellh/mapstructure"
	"minesweep/common/logx"
	"minesweep/common/safe"
	"minesweep/common/tools"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
	"minesweep/model/dao"
	"minesweep/usermgr"
	"time"
)

// ProcessMessage 处理客户端请求
func (slf *VoiceRoom) ProcessMessage(msg *request.PackMessage) {
	slf.Lock()
	defer slf.Unlock()
	defer safe.RecoverPanic()
	logx.Infof("PlatRoomID:%v ProcessMessage userID:%v, msg:%v", slf.PlatRoomID, msg.Ext.UserID, tools.GetObj(msg))

	slf.lastMsgTime = time.Now()
	switch msg.MsgID {
	case constvar.MsgTypeEnterVoiceRoom:
		slf.OnViewEnter(msg)
	case constvar.MsgTypeVoiceUserSit:
		slf.OnSitDown(msg)
	case constvar.MsgTypeVoiceUserStandUp:
		slf.OnStandUp(msg)
	case constvar.MsgTypeChangeVoiceCfg:
		slf.OnChangeCfg(msg)
	case constvar.MsgTypeVoiceUserReady:
		slf.OnUserReady(msg)
	case constvar.MsgTypeVoiceStartGame:
		slf.OnStartGame(msg)
	case constvar.MsgTypeUserOffline:
		slf.OnUserOffline(msg)
	case constvar.MsgTypeVoiceChangeRole:
		slf.OnChangeRole(msg)
	case constvar.MsgTypeVoiceKickOut:
		slf.OnKickOut(msg)
	case constvar.MsgTypeVoiceRoomInfo:
		slf.OnGetRoomInfo(msg)
	}
}

// OnViewEnter 观众进入
func (slf *VoiceRoom) OnViewEnter(msg *request.PackMessage) {
	params := &request.VoiceEnterRoom{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		logx.Infof("OnViewEnter PlatRoomID:%v Decode err:%v", slf.PlatRoomID, err)
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Infof("OnViewEnter PlatRoomID:%v userID:%v usermgr no user", slf.PlatRoomID, msg.Ext.UserID)
		return
	}

	if len(params.RoomID) == 0 || params.RoomID != user.PlatRoomID {
		logx.Infof("OnViewEnter PlatRoomID:%v userID:%v params:%v err", slf.PlatRoomID, user.UserID, params)
		user.SendMessage(msg.MsgID, ecode.ErrParams, struct{}{})
		return
	}

	// 观众进入
	slf.UserJoin(&VoiceUser{
		UserID:       user.UserID,
		Nickname:     user.Nickname,
		Avatar:       user.Avatar,
		Pos:          -1,
		Ready:        false,
		IdentityType: constvar.IdentityTypeUser,
		Role:         user.Role,
	})

	playerCount, _, viewerCount := slf.GetPlayerCount()
	if playerCount+viewerCount == 1 {
		// 仅halame渠道需要
		slf.ReportConfig()
	}
	logx.Infof("OnViewEnter PlatRoomID:%v userID:%v success, ConnSrvID:%v", slf.PlatRoomID, user.UserID, user.ConnSrvID)
	user.SendMessage(constvar.MsgTypeEnterVoiceRoom, ecode.OK, slf.GetRoomInfo())
}

// OnSitDown 坐到游戏位上, 坐下时不判断钱是否够
func (slf *VoiceRoom) OnSitDown(msg *request.PackMessage) {
	params := &request.VoiceUserSit{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Infof("PlatRoomID:%v userID:%v usermgr no user", slf.PlatRoomID, msg.Ext.UserID)
		return
	}

	errCode := slf.UserSitDown(user, params.Pos)
	if errCode != ecode.OK {
		logx.Infof("PlatRoomID:%v userID:%v UserSitDown errCode:%v, params:%v", slf.PlatRoomID, user.UserID, errCode, params)
		user.SendMessage(msg.MsgID, errCode, struct{}{})
		return
	}

	voiceUser := slf.GetUser(user.UserID)
	if voiceUser == nil {
		logx.Infof("PlatRoomID:%v userID:%v GetUser err", slf.PlatRoomID, user.UserID)
		return
	}

	logx.Infof("PlatRoomID:%v userID:%v OnSitDown success, params:%v", slf.PlatRoomID, user.UserID, params)
	slf.Broadcast(constvar.MsgTypeVoiceUserSit, ecode.OK, &response.VoiceRoomUser{
		UserID:   voiceUser.UserID,
		NickName: voiceUser.Nickname,
		Avatar:   voiceUser.Avatar,
		Pos:      voiceUser.Pos,
		Ready:    voiceUser.Ready,
		Robot:    voiceUser.IsRobot(),
		Role:     voiceUser.Role,
	})
}

// OnStandUp 玩家站起
func (slf *VoiceRoom) OnStandUp(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Infof("PlatRoomID:%v userID:%v usermgr no user", slf.PlatRoomID, msg.Ext.UserID)
		return
	}

	errCode := slf.UserStandUp(user.UserID)
	if errCode != ecode.OK {
		logx.Infof("PlatRoomID:%v userID:%v UserStandUp errCode:%v", slf.PlatRoomID, user.UserID, errCode)
		user.SendMessage(msg.MsgID, errCode, struct{}{})
		return
	}

	logx.Infof("PlatRoomID:%v userID:%v OnStandUp success", slf.PlatRoomID, user.UserID)
	slf.Broadcast(constvar.MsgTypeVoiceUserStandUp, ecode.OK, &response.NoticeByUserID{UserID: user.UserID})
}

// OnChangeCfg 更改语聊房的配置
func (slf *VoiceRoom) OnChangeCfg(msg *request.PackMessage) {
	params := &request.VoiceChangeRoomCfg{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Infof("PlatRoomID:%v userID:%v usermgr no user", slf.PlatRoomID, msg.Ext.UserID)
		return
	}

	voiceUser := slf.GetUser(user.UserID)
	if voiceUser == nil || !voiceUser.IsAdmin() {
		logx.Infof("PlatRoomID:%v userID:%v no power", slf.PlatRoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrVoiceNoPower, struct{}{})
		return
	}

	// 校验请求参数
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	roomConf := channelCfg.GetRoomConf(constvar.RoomTypeVoice)
	if roomConf == nil {
		logx.Infof("PlatRoomID:%v userID:%v no find roomConf", slf.PlatRoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrRoomConfig, struct{}{})
		return
	}
	if !tools.IsContain[int](roomConf.Fees, params.Fee) ||
		!tools.IsContain[int](roomConf.PlayerNums, params.PlayerNum) {
		logx.Infof("PlatRoomID:%v userID:%v params:%v err", slf.PlatRoomID, user.UserID, params)
		user.SendMessage(msg.MsgID, ecode.ErrParams, struct{}{})
		return
	}

	newViewerIds, errCode := slf.ChangeCfg(params)
	if errCode != ecode.OK {
		logx.Infof("PlatRoomID:%v userID:%v ChangeCfg errCode:%v, params:%v", slf.PlatRoomID, user.UserID, errCode, params)
		user.SendMessage(msg.MsgID, errCode, struct{}{})
		return
	}

	logx.Infof("PlatRoomID:%v userID:%v OnChangeCfg success, params:%v", slf.PlatRoomID, user.UserID, params)
	slf.Broadcast(constvar.MsgTypeChangeVoiceCfg, ecode.OK, &response.NoticeVoiceRoomCfg{
		RoomInfo:   *slf.GetRoomInfo(),
		NewViewers: newViewerIds,
	})
}

// OnUserReady 玩家准备
func (slf *VoiceRoom) OnUserReady(msg *request.PackMessage) {
	params := &request.VoiceUserReady{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Infof("PlatRoomID:%v userID:%v usermgr no user", slf.PlatRoomID, msg.Ext.UserID)
		return
	}

	if params.Ready && user.Coin < int64(slf.Fee) {
		user.SendMessage(msg.MsgID, ecode.ErrVoiceNotEnoughCoin, struct{}{})
		return
	}

	errCode := slf.UserReady(user.UserID, params.Ready)
	if errCode != ecode.OK {
		logx.Infof("PlatRoomID:%v userID:%v UserReady errCode:%v, params:%v", slf.PlatRoomID, user.UserID, errCode, params)
		user.SendMessage(msg.MsgID, errCode, struct{}{})
		return
	}

	logx.Infof("PlatRoomID:%v userID:%v OnUserReady success, params:%v", slf.PlatRoomID, user.UserID, params)
	slf.Broadcast(constvar.MsgTypeVoiceUserReady, ecode.OK, &response.NoticeVoiceUserStatus{
		UserID: user.UserID,
		Ready:  params.Ready,
	})
}

// OnStartGame 开始游戏
func (slf *VoiceRoom) OnStartGame(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Infof("PlatRoomID:%v userID:%v usermgr no user", slf.PlatRoomID, msg.Ext.UserID)
		return
	}

	voiceUser := slf.GetUser(user.UserID)
	if voiceUser == nil || !voiceUser.IsAdmin() {
		logx.Infof("PlatRoomID:%v userID:%v no power", slf.PlatRoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrVoiceNoPower, struct{}{})
		return
	}

	errCode := slf.StartGame()
	if errCode != ecode.OK {
		logx.Infof("PlatRoomID:%v userID:%v StartGame errCode:%v", slf.PlatRoomID, user.UserID, errCode)
		user.SendMessage(msg.MsgID, errCode, struct{}{})
		return
	}

	logx.Infof("PlatRoomID:%v userID:%v OnStartGame success", slf.PlatRoomID, user.UserID)
	user.SendMessage(constvar.MsgTypeVoiceStartGame, ecode.OK, struct{}{})
}

// OnUserOffline 玩家离线
func (slf *VoiceRoom) OnUserOffline(msg *request.PackMessage) {
	errCode := slf.UserOffline(msg.Ext.UserID)
	if errCode != ecode.OK {
		logx.Infof("PlatRoomID:%v userID:%v UserOffline errCode:%v", slf.PlatRoomID, msg.Ext.UserID, errCode)
		return
	}
}

// OnKickOut 踢除玩家
func (slf *VoiceRoom) OnKickOut(msg *request.PackMessage) {
	params := &request.VoiceKickOut{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Infof("PlatRoomID:%v userID:%v usermgr no user", slf.PlatRoomID, msg.Ext.UserID)
		return
	}

	if len(params.UserID) == 0 {
		logx.Infof("PlatRoomID:%v userID:%v params:%v err", slf.PlatRoomID, user.UserID, params)
		user.SendMessage(msg.MsgID, ecode.ErrParams, struct{}{})
		return
	}

	errCode := slf.UserKickOut(user.UserID, params.UserID)
	if errCode != ecode.OK {
		logx.Infof("PlatRoomID:%v userID:%v UserKickOut errCode:%v, params:%v", slf.PlatRoomID, user.UserID, errCode, params)
		user.SendMessage(msg.MsgID, errCode, struct{}{})
		return
	}

	logx.Infof("PlatRoomID:%v userID:%v OnKickOut success, params:%v", slf.PlatRoomID, user.UserID, params)
	slf.Broadcast(constvar.MsgTypeVoiceKickOut, ecode.OK, &response.NoticeVoiceKickOut{UserID: params.UserID, Operator: user.UserID})
}

// OnChangeRole 变更用户身份
func (slf *VoiceRoom) OnChangeRole(msg *request.PackMessage) {
	params := &request.VoiceChangeRole{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Infof("PlatRoomID:%v userID:%v usermgr no user", slf.PlatRoomID, msg.Ext.UserID)
		return
	}

	if !params.Role.Valid() {
		logx.Infof("PlatRoomID:%v userID:%v params:%v err", slf.PlatRoomID, user.UserID, params)
		user.SendMessage(msg.MsgID, ecode.ErrParams, struct{}{})
		return
	}

	user.ChangeRole(params.Role)
	slf.ChangeRole(user.UserID, params.Role)

	logx.Infof("PlatRoomID:%v userID:%v OnChangeRole success, params:%v", slf.PlatRoomID, user.UserID, params)
}

// OnGetRoomInfo 获取语聊房信息
func (slf *VoiceRoom) OnGetRoomInfo(msg *request.PackMessage) {
	params := &request.VoiceRoomInfo{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Infof("PlatRoomID:%v userID:%v usermgr no user", slf.PlatRoomID, msg.Ext.UserID)
		return
	}

	logx.Infof("PlatRoomID:%v userID:%v OnGetRoomInfo success, params:%v", slf.PlatRoomID, user.UserID, params)
	user.SendMessage(constvar.MsgTypeVoiceRoomInfo, ecode.OK, slf.GetRoomInfo())
}
