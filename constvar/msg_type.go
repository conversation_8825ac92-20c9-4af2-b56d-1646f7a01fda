package constvar

const MsgTypeCreateWs = "CreateWs"                    // 创建ws连接
const MsgTypeNoticeUserCoin string = "NoticeUserCoin" // 同步玩家金币
const MsgTypeHeartbeat string = "Heartbeat"           // 心跳
const MsgTypeLogin string = "Login"                   // 玩家登录
const MsgTypeUserInfo string = "UserInfo"             // 玩家请求自己信息
const MsgTypePairRequest string = "PairRequest"       // 玩家请求匹配
const MsgTypeCancelPair string = "CancelPair"         // 玩家取消匹配
const MsgTypePairResult string = "PairResult"         // 服务通知客户端匹配到了其他玩家

const MsgTypeCreateInvite string = "CreateInvite"             // 创建邀请
const MsgTypeAcceptInvite string = "AcceptInvite"             // 接受邀请
const MsgTypeInviteReady string = "InviteReady"               // 邀请者准备
const MsgTypeChgInviteCfg string = "ChgInviteCfg"             // 邀请创建者更改玩法配置
const MsgTypeLeaveInvite string = "LeaveInvite"               // 离开邀请
const MsgTypeNoticeInviteStatus string = "NoticeInviteStatus" // 广播邀请状态
const MsgTypeInviteKickOut string = "InviteKickOut"           // 邀请创建者踢出玩家
const MsgTypeInviteStart string = "InviteStart"               // 邀请创建者开始游戏

const MsgTypeViewerList string = "ViewerList"     // 旁观者列表
const MsgTypeEnterRoom string = "EnterRoom"       // 玩家请求进入房间
const MsgTypeSitDown string = "SitDown"           // 玩家请求坐下
const MsgTypeRobotSitDown string = "RobotSitDown" // 机器人请求坐下
const MsgTypeStand string = "Stand"               // 玩家请求站起
const MsgTypeReady string = "Ready"               // 玩家请求准备
const MsgTypeLeaveRoom string = "LeaveRoom"       // 玩家主动离开房间
const MsgTypeKickOutUser string = "KickOutUser"   // 玩家被踢出房间
const MsgTypeUserOffline string = "UserOffline"   // 玩家离线
const MsgTypeLoadConfig string = "LoadConfig"     // 加载配置

const MsgTypeProductList string = "ProductConfigs" // 玩家请求商品列表
const MsgTypeBuyProduct string = "BuyProduct"      // 玩家请求购买商品
const MsgTypeSetSkin string = "SetSkin"            // 设置皮肤
const MsgTypeGivingGift string = "GivingGift"      // 送礼物

const MsgTypeEnterVoiceRoom string = "EnterVoiceRoom"     // 进入语聊房
const MsgTypeVoiceUserSit string = "VoiceUserSit"         // 玩家请求坐下
const MsgTypeVoiceUserStandUp string = "VoiceUserStandUp" // 玩家请求站起
const MsgTypeChangeVoiceCfg string = "ChangeVoiceCfg"     // 修改语聊房配置
const MsgTypeVoiceUserReady string = "VoiceUserReady"     // 玩家请求准备
const MsgTypeVoiceStartGame string = "VoiceStartGame"     // 语聊房开始游戏
const MsgTypeVoiceRoomInfo string = "VoiceRoomInfo"       // 获取语聊房信息
const MsgTypeVoiceChangeRole string = "VoiceChangeRole"   // 变更Role
const MsgTypeVoiceKickOut string = "VoiceKickOut"         // 踢除玩家
const MsgTypeForceCloseGame string = "ForceCloseGame"     // 强制关闭游戏
const MsgTypeForceUserLeave string = "ForceUserLeave"     // 用户主动关闭游戏

const MsgTypeGameStart string = "GameStart"               // 开始游戏
const MsgTypeFirstMove string = "FirstMove"               // 先手
const MsgTypeFirstMoveEnd string = "FirstMoveEnd"         // 先手动画结束
const MsgTypeUserPosList string = "UserPosList"           // 玩家座位号列表
const MsgTypeRollDice string = "RollDice"                 // 掷骰子
const MsgTypeMoveChess string = "MoveChess"               // 移动棋子
const MsgTypeMoveChessEnd string = "MoveChessEnd"         // 移动棋子结束
const MsgTypeUseProp string = "UseProp"                   // 使用道具
const MsgTypeChoiceProp string = "ChoiceProp"             // 挑选道具
const MsgTypeChoicePropResult string = "ChoicePropResult" // 挑选道具的结果
const MsgTypeChoiceAdvance string = "ChoiceAdvance"       // 选择前进点数

// 扫雷游戏消息类型
const MsgTypeClickBlock string = "ClickBlock"                         // 点击方块
const MsgTypeMarkMine string = "MarkMine"                             // 标记地雷
const MsgTypeNoticeRoundStart string = "NoticeRoundStart"             // 回合开始通知
const MsgTypeNoticeActionDisplay string = "NoticeActionDisplay"       // 操作展示通知（第20秒）
const MsgTypeNoticeRoundEnd string = "NoticeRoundEnd"                 // 回合结束通知（第25秒）
const MsgTypeNoticeGameEnd string = "NoticeGameEnd"                   // 游戏结束通知
const MsgTypeNoticeFirstChoiceBonus string = "NoticeFirstChoiceBonus" // 首选玩家奖励推送通知

const MsgTypeSettlement string = "Settlement" // 大结算
const MsgTypeReqSmartOp string = "ReqSmartOp" // 请求智能操作
