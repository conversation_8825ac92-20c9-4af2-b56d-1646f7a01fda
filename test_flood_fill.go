package main

import (
	"fmt"
	"math/rand"
	"time"
)

// 简化的测试结构，不依赖外部日志系统
type TestMineBlock struct {
	X             int
	Y             int
	IsMine        bool
	IsRevealed    bool
	NeighborMines int
}

type TestMineMap struct {
	Width         int
	Height        int
	MineCount     int
	Blocks        [][]TestMineBlock
	RevealedCount int
}

func NewTestMineMap() *TestMineMap {
	const (
		mapWidth  = 8
		mapHeight = 8
		mineCount = 13
	)

	mineMap := &TestMineMap{
		Width:         mapWidth,
		Height:        mapHeight,
		MineCount:     mineCount,
		Blocks:        make([][]TestMineBlock, mapHeight),
		RevealedCount: 0,
	}

	// 初始化地图块
	for y := 0; y < mapHeight; y++ {
		mineMap.Blocks[y] = make([]TestMineBlock, mapWidth)
		for x := 0; x < mapWidth; x++ {
			mineMap.Blocks[y][x] = TestMineBlock{
				X:             x,
				Y:             y,
				IsMine:        false,
				IsRevealed:    false,
				NeighborMines: 0,
			}
		}
	}

	// 生成地雷分布
	mineMap.generateMines()
	// 计算每个格子周围的地雷数量
	mineMap.calculateNeighborMines()

	return mineMap
}

func (m *TestMineMap) generateMines() {
	rand.Seed(time.Now().UnixNano())

	totalPositions := m.Width * m.Height
	positions := make([]int, totalPositions)
	for i := 0; i < totalPositions; i++ {
		positions[i] = i
	}

	rand.Shuffle(totalPositions, func(i, j int) {
		positions[i], positions[j] = positions[j], positions[i]
	})

	minePositions := positions[:m.MineCount]

	for _, pos := range minePositions {
		x := pos % m.Width
		y := pos / m.Width
		m.Blocks[y][x].IsMine = true
	}
}

func (m *TestMineMap) calculateNeighborMines() {
	directions := [][]int{
		{-1, -1}, {-1, 0}, {-1, 1},
		{0, -1}, {0, 1},
		{1, -1}, {1, 0}, {1, 1},
	}

	for y := 0; y < m.Height; y++ {
		for x := 0; x < m.Width; x++ {
			if m.Blocks[y][x].IsMine {
				continue
			}

			mineCount := 0
			for _, dir := range directions {
				nx, ny := x+dir[1], y+dir[0]
				if nx >= 0 && nx < m.Width && ny >= 0 && ny < m.Height {
					if m.Blocks[ny][nx].IsMine {
						mineCount++
					}
				}
			}
			m.Blocks[y][x].NeighborMines = mineCount
		}
	}
}

func (m *TestMineMap) IsValidPosition(x, y int) bool {
	return x >= 0 && x < m.Width && y >= 0 && y < m.Height
}

func (m *TestMineMap) ProcessFloodFill(startX, startY int) []TestMineBlock {
	if !m.IsValidPosition(startX, startY) {
		return []TestMineBlock{}
	}

	startBlock := &m.Blocks[startY][startX]
	if startBlock.IsMine || startBlock.NeighborMines != 0 {
		return []TestMineBlock{}
	}

	directions := [][]int{
		{-1, -1}, {-1, 0}, {-1, 1},
		{0, -1}, {0, 1},
		{1, -1}, {1, 0}, {1, 1},
	}

	var revealedBlocks []TestMineBlock
	visited := make(map[string]bool)
	queue := [][]int{{startX, startY}}

	startKey := fmt.Sprintf("%d,%d", startX, startY)
	visited[startKey] = true

	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:]

		x, y := current[0], current[1]

		for _, dir := range directions {
			nx, ny := x+dir[0], y+dir[1]
			neighborKey := fmt.Sprintf("%d,%d", nx, ny)

			if visited[neighborKey] || !m.IsValidPosition(nx, ny) {
				continue
			}

			visited[neighborKey] = true
			neighborBlock := &m.Blocks[ny][nx]

			if neighborBlock.IsMine || neighborBlock.IsRevealed {
				continue
			}

			neighborBlock.IsRevealed = true
			m.RevealedCount++

			revealedBlocks = append(revealedBlocks, *neighborBlock)

			if neighborBlock.NeighborMines == 0 {
				queue = append(queue, []int{nx, ny})
			}
		}
	}

	return revealedBlocks
}

func main() {
	// 创建一个测试地图
	mineMap := NewTestMineMap()

	fmt.Printf("地图创建成功，大小: %dx%d，地雷数量: %d\n",
		mineMap.Width, mineMap.Height, mineMap.MineCount)

	// 打印地雷分布
	fmt.Println("\n地雷分布:")
	for y := 0; y < mineMap.Height; y++ {
		for x := 0; x < mineMap.Width; x++ {
			if mineMap.Blocks[y][x].IsMine {
				fmt.Print("* ")
			} else {
				fmt.Printf("%d ", mineMap.Blocks[y][x].NeighborMines)
			}
		}
		fmt.Println()
	}

	// 寻找一个空白格（周围地雷数为0）进行测试
	var testX, testY int = -1, -1
	for y := 0; y < mineMap.Height; y++ {
		for x := 0; x < mineMap.Width; x++ {
			if !mineMap.Blocks[y][x].IsMine && mineMap.Blocks[y][x].NeighborMines == 0 {
				testX, testY = x, y
				break
			}
		}
		if testX != -1 {
			break
		}
	}

	if testX == -1 {
		fmt.Println("\n没有找到空白格，无法测试连锁展开")
		return
	}

	fmt.Printf("\n找到空白格位置: (%d, %d)\n", testX, testY)

	// 先手动揭示起始点
	mineMap.Blocks[testY][testX].IsRevealed = true
	mineMap.RevealedCount++

	// 测试连锁展开
	result := mineMap.ProcessFloodFill(testX, testY)

	fmt.Printf("\n连锁展开结果:\n")
	fmt.Printf("触发位置: (%d, %d)\n", testX, testY)
	fmt.Printf("揭示方块数: %d\n", len(result))

	fmt.Println("\n揭示的方块详情:")
	for i, block := range result {
		fmt.Printf("%d. 位置:(%d,%d) 周围地雷数:%d\n",
			i+1, block.X, block.Y, block.NeighborMines)
	}

	// 打印揭示后的地图状态
	fmt.Println("\n揭示后的地图状态 (已揭示的显示数字, *=地雷, .=未揭示):")
	for y := 0; y < mineMap.Height; y++ {
		for x := 0; x < mineMap.Width; x++ {
			block := &mineMap.Blocks[y][x]
			if block.IsRevealed {
				if block.IsMine {
					fmt.Print("* ")
				} else {
					fmt.Printf("%d ", block.NeighborMines)
				}
			} else {
				fmt.Print(". ")
			}
		}
		fmt.Println()
	}

	fmt.Printf("\n总揭示方块数: %d\n", mineMap.RevealedCount)
}
